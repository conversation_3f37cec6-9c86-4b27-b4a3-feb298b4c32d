[workspace]
resolver = "2"
members = [
  "cli/kazuka-simple-arbitrage",
  "crates/kazuka-core",
  "crates/kazuka-mev-share",
  "crates/kazuka-mev-share-backend",
  "crates/kazuka-mev-share-rpc-api",
  "crates/kazuka-mev-share-sse",
  "crates/strategies/kazuka-mev-share-arbitrage",
]

default-members = ["cli/kazuka-simple-arbitrage"]

[workspace.dependencies]
# kazuka
kazuka-core = { path = "crates/kazuka-core" }
kazuka-mev-share = { path = "crates/kazuka-mev-share" }
kazuka-mev-share-sse = { path = "crates/kazuka-mev-share-sse" }
kazuka-mev-share-rpc-api = { path = "crates/kazuka-mev-share-rpc-api" }
kazuka-mev-share-backend = { path = "crates/kazuka-mev-share-backend" }
kazuka-mev-share-arbitrage = { path = "crates/strategies/kazuka-mev-share-arbitrage" }

# core
once_cell = "1.21"
num-traits = "0.2"
bytes = "1.10"
pin-project-lite = "0.2"
tracing = "0.1"
tracing-subscriber = { version = "0.3", default-features = false, features = [
  "fmt",
  "env-filter",
] }

# error
thiserror = "2.0"
anyhow = "1.0"

# benchmarking
divan = "0.1"
criterion = { version = "0.7", features = ["html_reports"] }

# async
async-trait = "0.1"
tokio = { version = "1.47", features = ["full"] }
tokio-util = { version = "0.7", features = ["compat"] }
tokio-stream = { version = "0.1", features = ["sync"] }
futures = "0.3"
futures-util = "0.3"

# network
jsonrpsee = { version = "0.26", features = ["client", "async-client"] }
hyper = "1.7"
tower = "0.5"
tower-http = "0.6"
http = "1.3"
http-types = "2.12"
http-body = "1.0"
http-body-util = "0.1"
reqwest = { version = "0.12", default-features = false, features = [
  "rustls-tls",
] }

# serialization, fs
serde = { version = "1", features = ["derive"] }
serde_json = "1.0"
csv = "1.3"

# alloy
alloy = { version = "1.0", features = [
  "full",
  "providers",
  "rpc",
  "rpc-types-mev",
] }
alloy-node-bindings = { version = "1.0" }

# mev
# alloy-mev = "0.5"

# cli
clap = { version = "4.5", features = ["derive"] }

# testing
tower-test = "0.4"
pretty_assertions = "1.4"
wiremock = "0.6"

[profile.release]
panic = 'abort'

[profile.dev]
panic = 'abort'
