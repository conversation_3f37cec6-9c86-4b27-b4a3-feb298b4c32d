use std::{collections::HashMap, ops::Add, path::PathBuf, sync::Arc};

use alloy::{
    primitives::{Address, B256, Bytes, U256},
    providers::Provider,
    rpc::types::mev::{
        BundleItem, Inclusion, MevSendBundle, ProtocolVersion,
        SimBundleOverrides,
    },
};
use async_trait::async_trait;
use kazuka_core::{error::KazukaError, types::Strategy};
use kazuka_mev_share::rpc::MevApiClient;
use kazuka_mev_share_arbitrage_bindings::blind_arb::BlindArb::BlindArbInstance;

use crate::{
    contracts::ArbitrageContract,
    types::{Action, Event, UniswapV2PoolInfo, V2V3PoolRecord},
};

pub struct MevShareUniswapV2V3Arbitrage<P: Provider> {
    /// Exposes Ethereum JSON-RPC methods.
    provider: Arc<P>,
    /// Maps Uniswap V3 pool address to Uniswap V2 pool info.
    v3_address_to_v2_pool_info: HashMap<Address, UniswapV2PoolInfo>,
    /// Arbitrage contract.
    contract: ArbitrageContract<Arc<P>>,
    /// MEV-share client for bundle simulation.
    mev_client: Option<Box<dyn MevApiClient + Send + Sync>>,
    /// Whether to want to interact with a real arbitrage contract or just
    /// synthesize sample txs and log traces.
    dry_run: bool,
    /// Minimum profit threshold in wei to submit bundles.
    min_profit_wei: U256,
}

impl<P: Provider> MevShareUniswapV2V3Arbitrage<P> {
    pub fn new(
        provider: Arc<P>,
        arbitrage_contract_address: Address,
        dry_run: bool,
    ) -> Self {
        let instance = BlindArbInstance::new(
            arbitrage_contract_address,
            provider.clone(),
        );
        let contract = ArbitrageContract::new(provider.clone(), instance);
        Self {
            provider: provider.clone(),
            v3_address_to_v2_pool_info: HashMap::new(),
            contract,
            mev_client: None,
            dry_run,
            min_profit_wei: U256::from(10000000000000000u64), /* 0.01 ETH minimum profit */
        }
    }

    /// Sets the MEV client for bundle simulation.
    pub fn with_mev_client(
        mut self,
        mev_client: Box<dyn MevApiClient + Send + Sync>,
    ) -> Self {
        self.mev_client = Some(mev_client);
        self
    }

    /// Sets the minimum profit threshold.
    pub fn with_min_profit(mut self, min_profit_wei: U256) -> Self {
        self.min_profit_wei = min_profit_wei;
        self
    }

    /// Calculates optimal arbitrage sizes based on pool reserves and price
    /// impact.
    async fn calculate_optimal_sizes(
        &self,
        _v3_address: Address,
        _v2_pool_info: &UniswapV2PoolInfo,
    ) -> Result<Vec<U256>, KazukaError> {
        // Use more reasonable sizes focused on common arbitrage amounts
        let base_sizes = vec![
            U256::from(100000000000000000u64),  // 0.1 ETH
            U256::from(250000000000000000u64),  // 0.25 ETH
            U256::from(500000000000000000u64),  // 0.5 ETH
            U256::from(1000000000000000000u64), // 1 ETH
            U256::from(2000000000000000000u64), // 2 ETH
        ];

        // TODO: Implement actual pool reserve fetching and optimal size
        // calculation In a real implementation, you would:
        // 1. Fetch current pool reserves from both V2 and V3 pools
        // 2. Calculate price impact for each size
        // 3. Estimate gas costs
        // 4. Filter out unprofitable sizes

        tracing::debug!(
            "Calculated optimal sizes for arbitrage opportunity: {:?}",
            base_sizes
        );

        Ok(base_sizes)
    }

    /// Simulates a bundle to check profitability before submission.
    async fn simulate_bundle(
        &self,
        bundle: &MevSendBundle,
    ) -> Result<bool, KazukaError> {
        if let Some(mev_client) = &self.mev_client {
            let sim_overrides = SimBundleOverrides::default();

            match mev_client.sim_bundle(bundle.clone(), sim_overrides).await {
                Ok(sim_result) => {
                    tracing::info!(
                        "Bundle simulation result: {:?}",
                        sim_result
                    );

                    if !sim_result.success {
                        tracing::warn!(
                            "Bundle simulation failed: {:?}",
                            sim_result.error
                        );
                        return Ok(false);
                    }

                    // Check if profit meets minimum threshold
                    let is_profitable =
                        sim_result.profit >= self.min_profit_wei;

                    if !is_profitable {
                        tracing::info!(
                            "Bundle profit {} wei below minimum threshold {} wei",
                            sim_result.profit,
                            self.min_profit_wei
                        );
                    }

                    Ok(is_profitable)
                }
                Err(e) => {
                    tracing::error!("Bundle simulation error: {:?}", e);
                    // In case of simulation error, still allow submission in
                    // dry run mode
                    Ok(self.dry_run)
                }
            }
        } else {
            // No MEV client available, skip simulation
            tracing::debug!("No MEV client available for bundle simulation");
            Ok(true)
        }
    }

    /// Generates bundles of varying sizes to submit to the matchmaker.
    pub async fn generate_bundles(
        &self,
        v3_address: Address,
        tx_hash: B256,
    ) -> Result<Vec<MevSendBundle>, KazukaError> {
        let mut bundles = Vec::new();

        let v2_pool_info = self
            .v3_address_to_v2_pool_info
            .get(&v3_address)
            .expect("V3 pool address not found in mapping");

        let sizes = self
            .calculate_optimal_sizes(v3_address, v2_pool_info)
            .await?;

        tracing::info!(
            "Generating bundles to exploit arbitrage opportunity on Uniswap V3 pool at {:?} versus Uniswap V2 pool at {:?}",
            v3_address,
            v2_pool_info.v2_pool
        );

        let block_num = self.provider.get_block_number().await?;

        for size in sizes {
            let tx_bytes = if self.dry_run {
                Bytes::from_static(b"sample-tx")
            } else {
                self.contract
                    .generate_arbitrage_tx(v3_address, v2_pool_info, size)
                    .await?
            };

            let bundle_body = vec![
                BundleItem::Hash { hash: tx_hash },
                BundleItem::Tx {
                    tx: tx_bytes,
                    can_revert: false,
                },
            ];

            let bundle = MevSendBundle {
                protocol_version: ProtocolVersion::V0_1,
                inclusion: Inclusion {
                    block: block_num.add(1),
                    // Reduce validity window for better inclusion chances
                    max_block: Some(block_num.add(5)),
                },
                bundle_body,
                validity: None,
                privacy: None,
            };

            // Simulate bundle before adding to submission list
            if self.simulate_bundle(&bundle).await? {
                tracing::info!(
                    "Bundle with size {} ETH passed simulation, adding to submission queue",
                    size.to::<u64>() as f64 / 1e18
                );
                bundles.push(bundle);
            } else {
                tracing::debug!(
                    "Bundle with size {} ETH failed simulation or profitability check, skipping",
                    size.to::<u64>() as f64 / 1e18
                );
            }
        }

        if bundles.is_empty() {
            tracing::warn!(
                "No profitable bundles generated for arbitrage opportunity"
            );
        }

        Ok(bundles)
    }
}

#[async_trait]
impl<P: Provider> Strategy<Event, Action> for MevShareUniswapV2V3Arbitrage<P> {
    /// Syncs the initial state of the strategy.
    /// This is called once at startup, and loads pool information into memory.
    async fn sync_state(&mut self) -> Result<(), KazukaError> {
        let mut path = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
        let file_name =
            String::from("data/uniswap_v2_uniswap_v3_weth_pools.csv");
        path.push(file_name.clone());

        let mut reader = csv::Reader::from_path(path.clone()).map_err(|e| {
            KazukaError::CsvError(file_name.clone(), e.to_string())
        })?;

        for record in reader.deserialize() {
            let record: V2V3PoolRecord = record.map_err(|e| {
                KazukaError::CsvError(file_name.clone(), e.to_string())
            })?;
            self.v3_address_to_v2_pool_info.insert(
                record.v3_pool,
                UniswapV2PoolInfo {
                    v2_pool: record.v2_pool,
                    is_weth_token0: record.is_weth_token0,
                },
            );
        }

        Ok(())
    }

    /// Processes a MEV-share event, and return an action if needed.
    async fn process_event(&mut self, event: Event) -> Vec<Action> {
        match event {
            Event::MevShareEvent(event) => {
                tracing::trace!("Received MEV-share event: {:?}", event);
                // Skip if event has no logs.
                if event.logs.is_empty() {
                    return vec![];
                }
                let v3_address = event.logs[0].address;
                // Skip if address is not a V3 pool.
                if !self.v3_address_to_v2_pool_info.contains_key(&v3_address) {
                    return vec![];
                }

                tracing::info!(
                    "Found a V3 pool match at address {:?}, generating bundles",
                    v3_address
                );

                match self.generate_bundles(v3_address, event.hash).await {
                    Ok(bundles) => {
                        bundles.into_iter().map(Action::SubmitBundle).collect()
                    }
                    Err(e) => {
                        tracing::error!("Error generating bundles: {:?}", e);
                        vec![]
                    }
                }
            }
        }
    }
}
