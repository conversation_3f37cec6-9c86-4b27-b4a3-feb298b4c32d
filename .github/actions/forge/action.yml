name: Forge build and check
description: Install dependencies, build and check the forge project
inputs:
  root:
    description: The project's root path
    required: true

runs:
  using: "composite"
  steps:
    - name: Install dependencies
      shell: bash
      run: forge install --root ${{ inputs.root }}

    - name: Build contracts
      shell: bash
      run: forge build --sizes --root ${{ inputs.root }}

    - name: Check formatting
      shell: bash
      run: |
        forge fmt --check --root ${{ inputs.root }}
