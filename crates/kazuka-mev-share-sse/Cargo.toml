[package]
name = "kazuka-mev-share-sse"
version = "0.1.0"
edition = "2024"

[dependencies]
alloy = { workspace = true, features = ["rpc", "rpc-types", "rpc-types-mev"] }

# network
async-sse = { git = "https://github.com/vyorkin/async-sse.git", branch = "feature/upgrade-dependencies" }
http-types.workspace = true
http.workspace = true
reqwest = { workspace = true, default-features = false, features = [
  "stream",
  "json",
] }

## async
tokio = { workspace = true, features = ["time"] }
tower = { workspace = true, optional = true }
hyper = { workspace = true, optional = true }
tokio-util = { workspace = true, features = ["compat"], optional = true }
tokio-stream = { workspace = true, features = ["sync"], optional = true }
futures-util = { workspace = true, features = ["io"] }

# serde
serde.workspace = true
serde_json.workspace = true

# misc
num-traits.workspace = true
bytes.workspace = true
pin-project-lite.workspace = true
thiserror.workspace = true
tracing.workspace = true

[features]
default = ["rustls"]
rustls = ["reqwest/rustls-tls"]
native-tls = ["reqwest/native-tls"]
server = ["hyper", "tokio-stream", "tokio-util", "tower"]

[dev-dependencies]
tokio = { workspace = true, features = ["macros", "rt", "rt-multi-thread"] }
tracing-subscriber = { workspace = true, default-features = false, features = [
  "env-filter",
  "fmt",
] }
pretty_assertions.workspace = true
wiremock.workspace = true
anyhow.workspace = true
