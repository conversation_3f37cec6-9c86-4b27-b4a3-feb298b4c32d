[package]
name = "kazuka-mev-share-rpc-api"
version = "0.1.0"
edition = "2024"

[dependencies]
bytes.workspace = true
tracing.workspace = true

tokio.workspace = true
async-trait.workspace = true
futures-util.workspace = true

hyper.workspace = true
tower = { workspace = true, features = ["util"] }
jsonrpsee = { workspace = true, features = ["macros", "http-client", "server"] }
http.workspace = true
http-body-util.workspace = true

serde.workspace = true

alloy = { workspace = true, features = ["rpc-types-mev"] }

[dev-dependencies]
pretty_assertions.workspace = true
anyhow.workspace = true
tracing-subscriber.workspace = true

[features]
default = ["client", "server", "alloy/rpc-types-mev"]
client = ["jsonrpsee/client"]
server = ["jsonrpsee/server"]
