name: CI

permissions:
  contents: read
on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always
  FOUNDRY_PROFILE: ci

jobs:
  fmt:
    name: Format
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          submodules: true

      - name: Install nightly rust toolchain
        uses: dtolnay/rust-toolchain@nightly
        with:
          components: rustfmt

      - name: Check formatting
        run: cargo fmt -- --check

  clippy:
    name: Clippy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          submodules: true

      - name: Install nightly rust toolchain
        uses: dtolnay/rust-toolchain@nightly
        with:
          components: clippy

      - name: C<PERSON>
        uses: Swatinem/rust-cache@v2

      - name: <PERSON><PERSON>
        uses: giraffate/clippy-action@v1
        with:
          reporter: "github-pr-review"
          github_token: ${{ secrets.GITHUB_TOKEN }}

  test:
    name: Run tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          submodules: true

      - name: Install nightly rust toolchain
        uses: dtolnay/rust-toolchain@nightly

      - name: Install foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Rust cache
        uses: Swatinem/rust-cache@v2

      - uses: ./.github/actions/forge
        with:
          root: ./crates/strategies/kazuka-mev-share-arbitrage/contracts

      - uses: taiki-e/install-action@nextest
      - name: Run cargo tests
        run: cargo nextest run --all-features --no-fail-fast --workspace --locked

      - name: Run forge tests
        env:
          ETHERSCAN_API_KEY: ${{ secrets.ETHERSCAN_API_KEY }}
          ALCHEMY_API_KEY: ${{ secrets.ALCHEMY_API_KEY }}
          ********************************: ${{ secrets.******************************** }}
          INFURA_API_KEY: ${{ secrets.INFURA_API_KEY }}
          INFURA_ETHEREUM_MAINNET_RPC_URL: ${{ secrets.INFURA_ETHEREUM_MAINNET_RPC_URL }}
          FORK_BLOCK_NUMBER: ${{ secrets.FORK_BLOCK_NUMBER }}
        run: |
          forge test --etherscan-api-key "$ETHERSCAN_API_KEY" -vvv --no-restart --root ./crates/strategies/kazuka-mev-share-arbitrage/contracts
        id: test

  coverage:
    name: Coverage
    runs-on: ubuntu-latest
    container:
      image: xd009642/tarpaulin:develop-nightly
      options: --security-opt seccomp=unconfined
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          submodules: true

      - name: Install foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Mark repository as safe for git
        run: git config --global --add safe.directory $GITHUB_WORKSPACE

      - uses: ./.github/actions/forge
        with:
          root: ./crates/strategies/kazuka-mev-share-arbitrage/contracts

      - name: Generate coverage report
        run: |
          cargo +nightly tarpaulin --verbose --all-features --workspace --timeout 120 --out xml

      - name: Upload coverage report to codecov.io
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
