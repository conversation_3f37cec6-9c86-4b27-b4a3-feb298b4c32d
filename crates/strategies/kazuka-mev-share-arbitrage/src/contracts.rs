use alloy::{
    network::TransactionBuilder,
    primitives::{Address, Bytes, U256},
    providers::Provider,
    sol,
};
use kazuka_core::error::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use kazuka_mev_share_arbitrage_bindings::blind_arb::BlindArb::BlindArbInstance;

use crate::types::UniswapV2PoolInfo;

sol!(
    BlindArb,
    "./contracts/out/BlindArb.sol/BlindArb.json"
);

/// Wrapper to simplify working with `BlindArbInstance`.
pub(crate) struct ArbitrageContract<P: Provider> {
    provider: P,
    instance: BlindArbInstance<P>,
}

impl<P: Provider> ArbitrageContract<P> {
    pub(crate) fn new(provider: P, instance: BlindArbInstance<P>) -> Self {
        Self { provider, instance }
    }

    /// Calculates dynamic gas price based on expected profit and market
    /// conditions.
    async fn calculate_dynamic_gas_price(
        &self,
        expected_profit: U256,
    ) -> Result<U256, Ka<PERSON>Error> {
        let base_gas_price = self.provider.get_gas_price().await?;

        // Use a more aggressive gas price for MEV opportunities
        // Bid up to 20% of expected profit on gas
        let max_gas_bid = expected_profit / U256::from(5); // 20% of profit
        let gas_limit = U256::from(400000); // Estimated gas limit
        let max_gas_price = max_gas_bid / gas_limit;

        // Use the higher of base gas price * 1.5 or calculated max gas price
        let aggressive_base = (base_gas_price * U256::from(3)) / U256::from(2); // 1.5x base
        let final_gas_price = aggressive_base.max(max_gas_price);

        tracing::debug!(
            "Gas pricing: base={}, aggressive_base={}, max_from_profit={}, final={}",
            base_gas_price,
            aggressive_base,
            max_gas_price,
            final_gas_price
        );

        Ok(final_gas_price)
    }

    pub(crate) async fn generate_arbitrage_tx(
        &self,
        v3_address: Address,
        v2_pool_info: &UniswapV2PoolInfo,
        size: U256,
    ) -> Result<Bytes, KazukaError> {
        // Calculate payment percentage based on size - larger trades pay more
        // to coinbase
        let payment_percentage = if size >= U256::from(1000000000000000000u64) {
            // >= 1 ETH: pay 10% to coinbase
            U256::from(10)
        } else if size >= U256::from(500000000000000000u64) {
            // >= 0.5 ETH: pay 5% to coinbase
            U256::from(5)
        } else {
            // < 0.5 ETH: pay 2% to coinbase
            U256::from(2)
        };

        // Estimate expected profit (simplified - in reality you'd calculate
        // this properly)
        let estimated_profit = size / U256::from(100); // Assume 1% profit
        let bid_gas_price =
            self.calculate_dynamic_gas_price(estimated_profit).await?;

        let mut tx = if v2_pool_info.is_weth_token0 {
            self.instance
                .execute_weth_token0(
                    v2_pool_info.v2_pool,
                    v3_address,
                    size,
                    payment_percentage,
                )
                .into_transaction_request()
        } else {
            self.instance
                .execute_weth_token1(
                    v2_pool_info.v2_pool,
                    v3_address,
                    size,
                    payment_percentage,
                )
                .into_transaction_request()
        };

        // Use more conservative gas limit with buffer
        tx.set_gas_limit(450000);
        tx.set_gas_price(bid_gas_price.to::<u128>());

        tracing::info!(
            "Generated arbitrage transaction for size {} ETH with {}% coinbase payment and {} gwei gas price",
            size.to::<u64>() as f64 / 1e18,
            payment_percentage,
            bid_gas_price.to::<u64>() as f64 / 1e9
        );

        let tx_bytes = self.provider.sign_transaction(tx).await?;
        Ok(tx_bytes)
    }
}
