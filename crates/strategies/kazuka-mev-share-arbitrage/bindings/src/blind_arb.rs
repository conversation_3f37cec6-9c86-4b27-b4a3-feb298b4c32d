/**

Generated by the following Solidity interface...
```solidity
interface BlindArb {
    event OwnershipTransferred(address indexed user, address indexed newOwner);

    constructor();

    receive() external payable;

    function execute_weth_token0(address v2Pool, address v3Pool, uint256 amountIn, uint256 percentageToPayToCoinbase) external;
    function execute_weth_token1(address v2Pool, address v3Pool, uint256 amountIn, uint256 percentageToPayToCoinbase) external;
    function owner() external view returns (address);
    function transferOwnership(address newOwner) external;
    function uniswapV3SwapCallback(int256 amount0Delta, int256 amount1Delta, bytes memory data) external;
    function withdrawETH() external;
    function withdrawWETH() external;
}
```

...which was generated by the following JSON ABI:
```json
[
  {
    "type": "constructor",
    "inputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "receive",
    "stateMutability": "payable"
  },
  {
    "type": "function",
    "name": "execute_weth_token0",
    "inputs": [
      {
        "name": "v2Pool",
        "type": "address",
        "internalType": "address"
      },
      {
        "name": "v3Pool",
        "type": "address",
        "internalType": "address"
      },
      {
        "name": "amountIn",
        "type": "uint256",
        "internalType": "uint256"
      },
      {
        "name": "percentageToPayToCoinbase",
        "type": "uint256",
        "internalType": "uint256"
      }
    ],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "execute_weth_token1",
    "inputs": [
      {
        "name": "v2Pool",
        "type": "address",
        "internalType": "address"
      },
      {
        "name": "v3Pool",
        "type": "address",
        "internalType": "address"
      },
      {
        "name": "amountIn",
        "type": "uint256",
        "internalType": "uint256"
      },
      {
        "name": "percentageToPayToCoinbase",
        "type": "uint256",
        "internalType": "uint256"
      }
    ],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "owner",
    "inputs": [],
    "outputs": [
      {
        "name": "",
        "type": "address",
        "internalType": "address"
      }
    ],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "transferOwnership",
    "inputs": [
      {
        "name": "newOwner",
        "type": "address",
        "internalType": "address"
      }
    ],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "uniswapV3SwapCallback",
    "inputs": [
      {
        "name": "amount0Delta",
        "type": "int256",
        "internalType": "int256"
      },
      {
        "name": "amount1Delta",
        "type": "int256",
        "internalType": "int256"
      },
      {
        "name": "data",
        "type": "bytes",
        "internalType": "bytes"
      }
    ],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "withdrawETH",
    "inputs": [],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "withdrawWETH",
    "inputs": [],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "event",
    "name": "OwnershipTransferred",
    "inputs": [
      {
        "name": "user",
        "type": "address",
        "indexed": true,
        "internalType": "address"
      },
      {
        "name": "newOwner",
        "type": "address",
        "indexed": true,
        "internalType": "address"
      }
    ],
    "anonymous": false
  }
]
```*/
#[allow(
    non_camel_case_types,
    non_snake_case,
    clippy::pub_underscore_fields,
    clippy::style,
    clippy::empty_structs_with_brackets
)]
pub mod BlindArb {
    use alloy::sol_types as alloy_sol_types;

    use super::*;
    /// The creation / init bytecode of the contract.
    ///
    /// ```text
    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
    /// ```
    #[rustfmt::skip]
    #[allow(clippy::all)]
    pub static BYTECODE: alloy_sol_types::private::Bytes = alloy_sol_types::private::Bytes::from_static(
        b"`\x80`@R4\x80\x15`\x0EW__\xFD[P3\x80__a\x01\0\n\x81T\x81s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x02\x19\x16\x90\x83s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x02\x17\x90UP\x80s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16_s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x7F\x8B\xE0\x07\x9CS\x16Y\x14\x13D\xCD\x1F\xD0\xA4\xF2\x84\x19I\x7F\x97\"\xA3\xDA\xAF\xE3\xB4\x18okdW\xE0`@Q`@Q\x80\x91\x03\x90\xA3Pa+\xD1\x80a\0\xB7_9_\xF3\xFE`\x80`@R`\x046\x10a\0sW_5`\xE0\x1C\x80c\xE0\x86\xE5\xEC\x11a\0MW\x80c\xE0\x86\xE5\xEC\x14a\0\xE6W\x80c\xED4\xB89\x14a\0\xFCW\x80c\xF2\xFD\xE3\x8B\x14a\x01$W\x80c\xFAF\x1E3\x14a\x01LWa\0zV[\x80c%;!\xCD\x14a\0~W\x80cL\x02\xF6.\x14a\0\xA6W\x80c\x8D\xA5\xCB[\x14a\0\xBCWa\0zV[6a\0zW\0[__\xFD[4\x80\x15a\0\x89W__\xFD[Pa\0\xA4`\x04\x806\x03\x81\x01\x90a\0\x9F\x91\x90a\x1D^V[a\x01tV[\0[4\x80\x15a\0\xB1W__\xFD[Pa\0\xBAa\x06\xC8V[\0[4\x80\x15a\0\xC7W__\xFD[Pa\0\xD0a\x08xV[`@Qa\0\xDD\x91\x90a\x1D\xD1V[`@Q\x80\x91\x03\x90\xF3[4\x80\x15a\0\xF1W__\xFD[Pa\0\xFAa\x08\x9CV[\0[4\x80\x15a\x01\x07W__\xFD[Pa\x01\"`\x04\x806\x03\x81\x01\x90a\x01\x1D\x91\x90a\x1D^V[a\tuV[\0[4\x80\x15a\x01/W__\xFD[Pa\x01J`\x04\x806\x03\x81\x01\x90a\x01E\x91\x90a\x1D\xEAV[a\x12\x93V[\0[4\x80\x15a\x01WW__\xFD[Pa\x01r`\x04\x806\x03\x81\x01\x90a\x01m\x91\x90a\x1E\xA9V[a\x13\xBDV[\0[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x02\x02W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x01\xF9\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x02P\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x02kW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x02\x8F\x91\x90a\x1F\xA6V[\x90P_`\x01s\xFF\xFD\x89c\xEF\xD1\xFCjPd\x88I]\x95\x1DRc\x98\x8D&a\x02\xB3\x91\x90a\x1F\xFEV[\x90P_3\x90P_\x81\x88\x88s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xD8\x91,\x10h\x1D\x8B!\xFD7B$ODe\x8D\xBA\x12&N\x8A_`@Q` \x01a\x02\xFF\x97\x96\x95\x94\x93\x92\x91\x90a nV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x90P__\x88s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x12\x8A\xCB\x08\x8B_\x8B\x89\x88`@Q\x86c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x03S\x95\x94\x93\x92\x91\x90a!iV[`@\x80Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x03nW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x03\x92\x91\x90a!\xD5V[\x91P\x91P_\x82a\x03\xA1\x90a\"\x13V[\x90P__\x8Cs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\t\x02\xF1\xAC`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01```@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x03\xEEW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x04\x12\x91\x90a\"\xD5V[Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91P_a\x04C\x84\x84\x84a\x19\x1DV[\x90P\x8Ds\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x02,\r\x9F_\x830`@Q\x84c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x04\x82\x93\x92\x91\x90a#\x8AV[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x04\x99W__\xFD[PZ\xF1\x15\x80\x15a\x04\xABW=__>=_\xFD[PPPP_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x04\xFD\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x05\x18W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x05<\x91\x90a\x1F\xA6V[\x90P_\x8B\x82a\x05K\x91\x90a#\xD2V[\x90P_\x81\x13a\x05\x8FW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x05\x86\x90a$\\V[`@Q\x80\x91\x03\x90\xFD[_`d\x8E\x83a\x05\x9E\x91\x90a$zV[a\x05\xA8\x91\x90a$\xE8V[\x90Ps\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c.\x1A}M\x82`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x05\xF7\x91\x90a%\x18V[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x06\x0EW__\xFD[PZ\xF1\x15\x80\x15a\x06 W=__>=_\xFD[PPPPAs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x08\xFC\x82\x90\x81\x15\x02\x90`@Q_`@Q\x80\x83\x03\x81\x85\x88\x88\xF1\x93PPPP\x15\x80\x15a\x06gW=__>=_\xFD[P\x8C\x81\x84a\x06u\x91\x90a%1V[\x11a\x06\xB5W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x06\xAC\x90a%\xAEV[`@Q\x80\x91\x03\x90\xFD[PPPPPPPPPPPPPPPPPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x07VW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x07M\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x07\xA4\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x07\xBFW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x07\xE3\x91\x90a\x1F\xA6V[\x90Ps\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\xA9\x05\x9C\xBB3\x83`@Q\x83c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x084\x92\x91\x90a%\xCCV[` `@Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x08PW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x08t\x91\x90a&\x1DV[PPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x81V[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\t*W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\t!\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_G\x90P3s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x08\xFC\x82\x90\x81\x15\x02\x90`@Q_`@Q\x80\x83\x03\x81\x85\x88\x88\xF1\x93PPPP\x15\x80\x15a\tqW=__>=_\xFD[PPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\n\x03W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\t\xFA\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\nQ\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\nlW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\n\x90\x91\x90a\x1F\xA6V[\x90P_`\x01d\x01\0\x02v\xA3a\n\xA5\x91\x90a&HV[\x90P_3\x90P_\x81\x88\x88s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xD8\x91,\x10h\x1D\x8B!\xFD7B$ODe\x8D\xBA\x12&N\x8A`\x01`@Q` \x01a\n\xF2\x97\x96\x95\x94\x93\x92\x91\x90a nV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x90Pa\x0B$`@Q\x80``\x01`@R\x80`*\x81R` \x01a+H`*\x919a\x19rV[a\x0Bc`@Q\x80`@\x01`@R\x80`\r\x81R` \x01\x7F  v2Pool = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x89a\x1A\x0BV[a\x0B\xA3`@Q\x80`@\x01`@R\x80`\x11\x81R` \x01\x7F  zeroForOne = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP`\x01a\x1A\xA7V[a\x0B\xE2`@Q\x80`@\x01`@R\x80`\x0F\x81R` \x01\x7F  amountIn = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x87a\x1BCV[a\x0C7`@Q\x80`@\x01`@R\x80`\x18\x81R` \x01\x7F  sqrtPriceLimitX96 = %s\0\0\0\0\0\0\0\0\x81RP\x84s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x1BCV[__\x88s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x12\x8A\xCB\x08\x8B`\x01\x8B\x89\x88`@Q\x86c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x0C{\x95\x94\x93\x92\x91\x90a!iV[`@\x80Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x0C\x96W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x0C\xBA\x91\x90a!\xD5V[\x91P\x91Pa\x0C\xFC`@Q\x80`@\x01`@R\x80` \x81R` \x01\x7FUniswapV3 pool WETH/PLU changes:\x81RPa\x19rV[a\r\x1E`@Q\x80``\x01`@R\x80`+\x81R` \x01a*o`+\x919\x83a\x1B\xDFV[a\r@`@Q\x80``\x01`@R\x80`*\x81R` \x01a+r`*\x919\x82a\x1B\xDFV[__\x8Bs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\t\x02\xF1\xAC`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01```@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\r\x8BW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\r\xAF\x91\x90a\"\xD5V[Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91Pa\r\xF5`@Q\x80``\x01`@R\x80`$\x81R` \x01a*\x9A`$\x919a\x19rV[a\x0E4`@Q\x80`@\x01`@R\x80`\x1F\x81R` \x01\x7F  wethReserveV2 = %s wei (WETH)\0\x81RP\x83a\x1BCV[a\x0Es`@Q\x80`@\x01`@R\x80`\x1E\x81R` \x01\x7F  pluReserveV2  = %s wei (PLU)\0\0\x81RP\x82a\x1BCV[_\x83a\x0E~\x90a\"\x13V[\x90P_a\x0E\x8C\x82\x84\x86a\x19\x1DV[\x90Pa\x0E\xAF`@Q\x80`\x80\x01`@R\x80`E\x81R` \x01a*\xE0`E\x919a\x19rV[a\x0E\xEE`@Q\x80`@\x01`@R\x80`\x1E\x81R` \x01\x7F  pluAmountIn   = %s wei (PLU)\0\0\x81RP\x83a\x1BCV[a\x0F-`@Q\x80`@\x01`@R\x80`\x1F\x81R` \x01\x7F  wethAmountOut = %s wei (WETH)\0\x81RP\x82a\x1BCV[a\x0Fk`@Q\x80`@\x01`@R\x80`\x1D\x81R` \x01\x7FIUniswapV2Pair(v2Pool).swap()\0\0\0\x81RPa\x19rV[\x8Ds\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x02,\r\x9F\x82_0`@Q\x84c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x0F\xA8\x93\x92\x91\x90a&\x8FV[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x0F\xBFW__\xFD[PZ\xF1\x15\x80\x15a\x0F\xD1W=__>=_\xFD[PPPP_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x10#\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x10>W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x10b\x91\x90a\x1F\xA6V[\x90P_\x8B\x82a\x10q\x91\x90a#\xD2V[\x90P_\x81\x13a\x10\xB5W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x10\xAC\x90a$\\V[`@Q\x80\x91\x03\x90\xFD[_`d\x8E\x83a\x10\xC4\x91\x90a$zV[a\x10\xCE\x91\x90a$\xE8V[\x90Ps\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c.\x1A}M\x82`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x11\x1D\x91\x90a%\x18V[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x114W__\xFD[PZ\xF1\x15\x80\x15a\x11FW=__>=_\xFD[PPPPAs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x08\xFC\x82\x90\x81\x15\x02\x90`@Q_`@Q\x80\x83\x03\x81\x85\x88\x88\xF1\x93PPPP\x15\x80\x15a\x11\x8DW=__>=_\xFD[Pa\x11\xB0`@Q\x80``\x01`@R\x80`#\x81R` \x01a+%`#\x919\x8Ea\x1BCV[a\x11\xD2`@Q\x80``\x01`@R\x80`\"\x81R` \x01a*M`\"\x919\x84a\x1BCV[a\x11\xF4`@Q\x80``\x01`@R\x80`\"\x81R` \x01a*\xBE`\"\x919\x82a\x1BCV[a\x123`@Q\x80`@\x01`@R\x80`\x18\x81R` \x01\x7F  profit = %s wei (WETH)\0\0\0\0\0\0\0\0\x81RP\x83a\x1B\xDFV[\x8C\x81\x84a\x12@\x91\x90a%1V[\x11a\x12\x80W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x12w\x90a%\xAEV[`@Q\x80\x91\x03\x90\xFD[PPPPPPPPPPPPPPPPPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x13!W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x13\x18\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[\x80__a\x01\0\n\x81T\x81s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x02\x19\x16\x90\x83s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x02\x17\x90UP\x80s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x7F\x8B\xE0\x07\x9CS\x16Y\x14\x13D\xCD\x1F\xD0\xA4\xF2\x84\x19I\x7F\x97\"\xA3\xDA\xAF\xE3\xB4\x18okdW\xE0`@Q`@Q\x80\x91\x03\x90\xA3PV[a\x13\xFB`@Q\x80`@\x01`@R\x80`\x17\x81R` \x01\x7FuniswapV3SwapCallback()\0\0\0\0\0\0\0\0\0\x81RPa\x19rV[a\x14:`@Q\x80`@\x01`@R\x80`\x13\x81R` \x01\x7F  amount0Delta = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x85a\x1B\xDFV[a\x14y`@Q\x80`@\x01`@R\x80`\x13\x81R` \x01\x7F  amount1Delta = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x84a\x1B\xDFV[_\x84\x13\x80\x15a\x14\x87WP_\x83\x12[\x80a\x14\x9CWP_\x84\x12\x80\x15a\x14\x9BWP_\x83\x13[[a\x14\xDBW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x14\xD2\x90a'!V[`@Q\x80\x91\x03\x90\xFD[_______\x88\x88\x81\x01\x90a\x14\xF1\x91\x90a'\x8EV[\x96P\x96P\x96P\x96P\x96P\x96P\x96P\x84s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x15mW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x15d\x90a(uV[`@Q\x80\x91\x03\x90\xFD[a\x15\xAB`@Q\x80`@\x01`@R\x80`\x16\x81R` \x01\x7FDecoded callback data:\0\0\0\0\0\0\0\0\0\0\x81RPa\x19rV[a\x15\xEA`@Q\x80`@\x01`@R\x80`\x12\x81R` \x01\x7F  beneficiary = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x88a\x1A\x0BV[a\x16)`@Q\x80`@\x01`@R\x80`\r\x81R` \x01\x7F  v2Pool = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x87a\x1A\x0BV[a\x16h`@Q\x80`@\x01`@R\x80`\r\x81R` \x01\x7F  v3Pool = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x86a\x1A\x0BV[a\x16\xA7`@Q\x80`@\x01`@R\x80`\x0E\x81R` \x01\x7F  tokenIn = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x85a\x1A\x0BV[a\x16\xE6`@Q\x80`@\x01`@R\x80`\x0F\x81R` \x01\x7F  tokenOut = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x84a\x1A\x0BV[a\x17%`@Q\x80`@\x01`@R\x80`\x0F\x81R` \x01\x7F  amountIn = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x83a\x1BCV[a\x17d`@Q\x80`@\x01`@R\x80`\x11\x81R` \x01\x7F  zeroForOne = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x82a\x1A\xA7V[___\x8D\x13a\x17\xE0W\x86s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\xD2\x12 \xA7`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x17\xB6W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x17\xDA\x91\x90a(\xA7V[\x8Ca\x18OV[\x86s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\r\xFE\x16\x81`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x18)W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x18M\x91\x90a(\xA7V[\x8D[\x91P\x91Pa\x18\x92`@Q\x80`@\x01`@R\x80`\x1D\x81R` \x01\x7FamountToRepay = %s wei (WETH)\0\0\0\x81RP\x82a\x1BCV[\x81s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\xA9\x05\x9C\xBB\x88\x83`@Q\x83c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x18\xCD\x92\x91\x90a%\xCCV[` `@Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x18\xE9W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x19\r\x91\x90a&\x1DV[PPPPPPPPPPPPPPV[__a\x03\xE5\x85a\x19-\x91\x90a$zV[\x90P_\x83\x82a\x19<\x91\x90a$zV[\x90P_\x82a\x03\xE8\x87a\x19N\x91\x90a$zV[a\x19X\x91\x90a(\xD2V[\x90P\x80\x82a\x19f\x91\x90a$\xE8V[\x93PPPP\x93\x92PPPV[a\x1A\x08\x81`@Q`$\x01a\x19\x86\x91\x90a)GV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7FA0O\xAC\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PV[a\x1A\xA3\x82\x82`@Q`$\x01a\x1A!\x92\x91\x90a)gV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F1\x9A\xF33\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1B?\x82\x82`@Q`$\x01a\x1A\xBD\x92\x91\x90a)\x95V[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F\xC3\xB5V5\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1B\xDB\x82\x82`@Q`$\x01a\x1BY\x92\x91\x90a)\xC3V[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F\xB6\x0Er\xCC\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1Cw\x82\x82`@Q`$\x01a\x1B\xF5\x92\x91\x90a)\xF1V[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F<\xA6&\x8E\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1C\x92\x81a\x1C\x8Aa\x1C\x95a\x1C\xB4V[c\xFF\xFF\xFF\xFF\x16V[PV[_jconsole.log\x90P__\x83Q` \x85\x01\x84Z\xFAPPPV[a\x1C\xBF\x81\x90P\x91\x90PV[a\x1C\xC7a*\x1FV[V[__\xFD[__\xFD[_s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x82\x16\x90P\x91\x90PV[_a\x1C\xFA\x82a\x1C\xD1V[\x90P\x91\x90PV[a\x1D\n\x81a\x1C\xF0V[\x81\x14a\x1D\x14W__\xFD[PV[_\x815\x90Pa\x1D%\x81a\x1D\x01V[\x92\x91PPV[_\x81\x90P\x91\x90PV[a\x1D=\x81a\x1D+V[\x81\x14a\x1DGW__\xFD[PV[_\x815\x90Pa\x1DX\x81a\x1D4V[\x92\x91PPV[____`\x80\x85\x87\x03\x12\x15a\x1DvWa\x1Dua\x1C\xC9V[[_a\x1D\x83\x87\x82\x88\x01a\x1D\x17V[\x94PP` a\x1D\x94\x87\x82\x88\x01a\x1D\x17V[\x93PP`@a\x1D\xA5\x87\x82\x88\x01a\x1DJV[\x92PP``a\x1D\xB6\x87\x82\x88\x01a\x1DJV[\x91PP\x92\x95\x91\x94P\x92PV[a\x1D\xCB\x81a\x1C\xF0V[\x82RPPV[_` \x82\x01\x90Pa\x1D\xE4_\x83\x01\x84a\x1D\xC2V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a\x1D\xFFWa\x1D\xFEa\x1C\xC9V[[_a\x1E\x0C\x84\x82\x85\x01a\x1D\x17V[\x91PP\x92\x91PPV[_\x81\x90P\x91\x90PV[a\x1E'\x81a\x1E\x15V[\x81\x14a\x1E1W__\xFD[PV[_\x815\x90Pa\x1EB\x81a\x1E\x1EV[\x92\x91PPV[__\xFD[__\xFD[__\xFD[__\x83`\x1F\x84\x01\x12a\x1EiWa\x1Eha\x1EHV[[\x825\x90Pg\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a\x1E\x86Wa\x1E\x85a\x1ELV[[` \x83\x01\x91P\x83`\x01\x82\x02\x83\x01\x11\x15a\x1E\xA2Wa\x1E\xA1a\x1EPV[[\x92P\x92\x90PV[____``\x85\x87\x03\x12\x15a\x1E\xC1Wa\x1E\xC0a\x1C\xC9V[[_a\x1E\xCE\x87\x82\x88\x01a\x1E4V[\x94PP` a\x1E\xDF\x87\x82\x88\x01a\x1E4V[\x93PP`@\x85\x015g\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a\x1F\0Wa\x1E\xFFa\x1C\xCDV[[a\x1F\x0C\x87\x82\x88\x01a\x1ETV[\x92P\x92PP\x92\x95\x91\x94P\x92PV[_\x82\x82R` \x82\x01\x90P\x92\x91PPV[\x7FUNAUTHORIZED\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a\x1F^`\x0C\x83a\x1F\x1AV[\x91Pa\x1Fi\x82a\x1F*V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra\x1F\x8B\x81a\x1FRV[\x90P\x91\x90PV[_\x81Q\x90Pa\x1F\xA0\x81a\x1D4V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a\x1F\xBBWa\x1F\xBAa\x1C\xC9V[[_a\x1F\xC8\x84\x82\x85\x01a\x1F\x92V[\x91PP\x92\x91PPV[\x7FNH{q\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_R`\x11`\x04R`$_\xFD[_a \x08\x82a\x1C\xD1V[\x91Pa \x13\x83a\x1C\xD1V[\x92P\x82\x82\x03\x90Ps\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a ?Wa >a\x1F\xD1V[[\x92\x91PPV[a N\x81a\x1D+V[\x82RPPV[_\x81\x15\x15\x90P\x91\x90PV[a h\x81a TV[\x82RPPV[_`\xE0\x82\x01\x90Pa \x81_\x83\x01\x8Aa\x1D\xC2V[a \x8E` \x83\x01\x89a\x1D\xC2V[a \x9B`@\x83\x01\x88a\x1D\xC2V[a \xA8``\x83\x01\x87a\x1D\xC2V[a \xB5`\x80\x83\x01\x86a\x1D\xC2V[a \xC2`\xA0\x83\x01\x85a EV[a \xCF`\xC0\x83\x01\x84a _V[\x98\x97PPPPPPPPV[a \xE4\x81a\x1E\x15V[\x82RPPV[a \xF3\x81a\x1C\xD1V[\x82RPPV[_\x81Q\x90P\x91\x90PV[_\x82\x82R` \x82\x01\x90P\x92\x91PPV[\x82\x81\x83^_\x83\x83\x01RPPPV[_`\x1F\x19`\x1F\x83\x01\x16\x90P\x91\x90PV[_a!;\x82a \xF9V[a!E\x81\x85a!\x03V[\x93Pa!U\x81\x85` \x86\x01a!\x13V[a!^\x81a!!V[\x84\x01\x91PP\x92\x91PPV[_`\xA0\x82\x01\x90Pa!|_\x83\x01\x88a\x1D\xC2V[a!\x89` \x83\x01\x87a _V[a!\x96`@\x83\x01\x86a \xDBV[a!\xA3``\x83\x01\x85a \xEAV[\x81\x81\x03`\x80\x83\x01Ra!\xB5\x81\x84a!1V[\x90P\x96\x95PPPPPPV[_\x81Q\x90Pa!\xCF\x81a\x1E\x1EV[\x92\x91PPV[__`@\x83\x85\x03\x12\x15a!\xEBWa!\xEAa\x1C\xC9V[[_a!\xF8\x85\x82\x86\x01a!\xC1V[\x92PP` a\"\t\x85\x82\x86\x01a!\xC1V[\x91PP\x92P\x92\x90PV[_a\"\x1D\x82a\x1E\x15V[\x91P\x7F\x80\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x82\x03a\"OWa\"Na\x1F\xD1V[[\x81_\x03\x90P\x91\x90PV[_m\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x82\x16\x90P\x91\x90PV[a\"{\x81a\"YV[\x81\x14a\"\x85W__\xFD[PV[_\x81Q\x90Pa\"\x96\x81a\"rV[\x92\x91PPV[_c\xFF\xFF\xFF\xFF\x82\x16\x90P\x91\x90PV[a\"\xB4\x81a\"\x9CV[\x81\x14a\"\xBEW__\xFD[PV[_\x81Q\x90Pa\"\xCF\x81a\"\xABV[\x92\x91PPV[___``\x84\x86\x03\x12\x15a\"\xECWa\"\xEBa\x1C\xC9V[[_a\"\xF9\x86\x82\x87\x01a\"\x88V[\x93PP` a#\n\x86\x82\x87\x01a\"\x88V[\x92PP`@a#\x1B\x86\x82\x87\x01a\"\xC1V[\x91PP\x92P\x92P\x92V[_\x81\x90P\x91\x90PV[_\x81\x90P\x91\x90PV[_a#Qa#La#G\x84a#%V[a#.V[a\x1D+V[\x90P\x91\x90PV[a#a\x81a#7V[\x82RPPV[PV[_a#u_\x83a!\x03V[\x91Pa#\x80\x82a#gV[_\x82\x01\x90P\x91\x90PV[_`\x80\x82\x01\x90Pa#\x9D_\x83\x01\x86a#XV[a#\xAA` \x83\x01\x85a EV[a#\xB7`@\x83\x01\x84a\x1D\xC2V[\x81\x81\x03``\x83\x01Ra#\xC8\x81a#jV[\x90P\x94\x93PPPPV[_a#\xDC\x82a\x1E\x15V[\x91Pa#\xE7\x83a\x1E\x15V[\x92P\x82\x82\x03\x90P\x81\x81\x12_\x84\x12\x16\x82\x82\x13_\x85\x12\x15\x16\x17\x15a$\x0CWa$\x0Ba\x1F\xD1V[[\x92\x91PPV[\x7Fno profit\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a$F`\t\x83a\x1F\x1AV[\x91Pa$Q\x82a$\x12V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra$s\x81a$:V[\x90P\x91\x90PV[_a$\x84\x82a\x1D+V[\x91Pa$\x8F\x83a\x1D+V[\x92P\x82\x82\x02a$\x9D\x81a\x1D+V[\x91P\x82\x82\x04\x84\x14\x83\x15\x17a$\xB4Wa$\xB3a\x1F\xD1V[[P\x92\x91PPV[\x7FNH{q\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_R`\x12`\x04R`$_\xFD[_a$\xF2\x82a\x1D+V[\x91Pa$\xFD\x83a\x1D+V[\x92P\x82a%\rWa%\x0Ca$\xBBV[[\x82\x82\x04\x90P\x92\x91PPV[_` \x82\x01\x90Pa%+_\x83\x01\x84a EV[\x92\x91PPV[_a%;\x82a\x1D+V[\x91Pa%F\x83a\x1D+V[\x92P\x82\x82\x03\x90P\x81\x81\x11\x15a%^Wa%]a\x1F\xD1V[[\x92\x91PPV[\x7Farbitrage failed\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a%\x98`\x10\x83a\x1F\x1AV[\x91Pa%\xA3\x82a%dV[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra%\xC5\x81a%\x8CV[\x90P\x91\x90PV[_`@\x82\x01\x90Pa%\xDF_\x83\x01\x85a\x1D\xC2V[a%\xEC` \x83\x01\x84a EV[\x93\x92PPPV[a%\xFC\x81a TV[\x81\x14a&\x06W__\xFD[PV[_\x81Q\x90Pa&\x17\x81a%\xF3V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a&2Wa&1a\x1C\xC9V[[_a&?\x84\x82\x85\x01a&\tV[\x91PP\x92\x91PPV[_a&R\x82a\x1C\xD1V[\x91Pa&]\x83a\x1C\xD1V[\x92P\x82\x82\x01\x90Ps\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a&\x89Wa&\x88a\x1F\xD1V[[\x92\x91PPV[_`\x80\x82\x01\x90Pa&\xA2_\x83\x01\x86a EV[a&\xAF` \x83\x01\x85a#XV[a&\xBC`@\x83\x01\x84a\x1D\xC2V[\x81\x81\x03``\x83\x01Ra&\xCD\x81a#jV[\x90P\x94\x93PPPPV[\x7Fnothing to repay\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a'\x0B`\x10\x83a\x1F\x1AV[\x91Pa'\x16\x82a&\xD7V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra'8\x81a&\xFFV[\x90P\x91\x90PV[_a'I\x82a\x1C\xD1V[\x90P\x91\x90PV[a'Y\x81a'?V[\x81\x14a'cW__\xFD[PV[_\x815\x90Pa't\x81a'PV[\x92\x91PPV[_\x815\x90Pa'\x88\x81a%\xF3V[\x92\x91PPV[_______`\xE0\x88\x8A\x03\x12\x15a'\xA9Wa'\xA8a\x1C\xC9V[[_a'\xB6\x8A\x82\x8B\x01a'fV[\x97PP` a'\xC7\x8A\x82\x8B\x01a'fV[\x96PP`@a'\xD8\x8A\x82\x8B\x01a'fV[\x95PP``a'\xE9\x8A\x82\x8B\x01a'fV[\x94PP`\x80a'\xFA\x8A\x82\x8B\x01a'fV[\x93PP`\xA0a(\x0B\x8A\x82\x8B\x01a\x1DJV[\x92PP`\xC0a(\x1C\x8A\x82\x8B\x01a'zV[\x91PP\x92\x95\x98\x91\x94\x97P\x92\x95PV[\x7Finvalid sender\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a(_`\x0E\x83a\x1F\x1AV[\x91Pa(j\x82a(+V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra(\x8C\x81a(SV[\x90P\x91\x90PV[_\x81Q\x90Pa(\xA1\x81a\x1D\x01V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a(\xBCWa(\xBBa\x1C\xC9V[[_a(\xC9\x84\x82\x85\x01a(\x93V[\x91PP\x92\x91PPV[_a(\xDC\x82a\x1D+V[\x91Pa(\xE7\x83a\x1D+V[\x92P\x82\x82\x01\x90P\x80\x82\x11\x15a(\xFFWa(\xFEa\x1F\xD1V[[\x92\x91PPV[_\x81Q\x90P\x91\x90PV[_a)\x19\x82a)\x05V[a)#\x81\x85a\x1F\x1AV[\x93Pa)3\x81\x85` \x86\x01a!\x13V[a)<\x81a!!V[\x84\x01\x91PP\x92\x91PPV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra)_\x81\x84a)\x0FV[\x90P\x92\x91PPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra)\x7F\x81\x85a)\x0FV[\x90Pa)\x8E` \x83\x01\x84a\x1D\xC2V[\x93\x92PPPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra)\xAD\x81\x85a)\x0FV[\x90Pa)\xBC` \x83\x01\x84a _V[\x93\x92PPPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra)\xDB\x81\x85a)\x0FV[\x90Pa)\xEA` \x83\x01\x84a EV[\x93\x92PPPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra*\t\x81\x85a)\x0FV[\x90Pa*\x18` \x83\x01\x84a \xDBV[\x93\x92PPPV[\x7FNH{q\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_R`Q`\x04R`$_\xFD\xFE  wethBalanceAfter = %s wei (WETH)  wethAmountDelta (amount0) = %s wei (WETH)IUniswapV2Pair(v2Pool).getReserves()  profitToCoinbase = %s wei (WETH)uniswapV2CalculateAmountOut(pluAmountIn, pluReserveV2, wethReserveV2)  wethBalanceBefore = %s wei (WETH)IUniswapV3Pool(v3Pool).swap [WETH -> PLU]:  pluAmountDelta (amount1)  = %s wei (PLU)\xA2dipfsX\"\x12 \xC38\x10\x82\x1C\xB2\xB1` \xF6*P\xB4\x1C^A\xE4\xFC\xD6\xA0\xE8iW\xEC\x9B\x90\x8B\x91s*\xF9\x92dsolcC\0\x08\x1E\x003",
    );
    /// The runtime bytecode of the contract, as deployed on the network.
    ///
    /// ```text
    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
    /// ```
    #[rustfmt::skip]
    #[allow(clippy::all)]
    pub static DEPLOYED_BYTECODE: alloy_sol_types::private::Bytes = alloy_sol_types::private::Bytes::from_static(
        b"`\x80`@R`\x046\x10a\0sW_5`\xE0\x1C\x80c\xE0\x86\xE5\xEC\x11a\0MW\x80c\xE0\x86\xE5\xEC\x14a\0\xE6W\x80c\xED4\xB89\x14a\0\xFCW\x80c\xF2\xFD\xE3\x8B\x14a\x01$W\x80c\xFAF\x1E3\x14a\x01LWa\0zV[\x80c%;!\xCD\x14a\0~W\x80cL\x02\xF6.\x14a\0\xA6W\x80c\x8D\xA5\xCB[\x14a\0\xBCWa\0zV[6a\0zW\0[__\xFD[4\x80\x15a\0\x89W__\xFD[Pa\0\xA4`\x04\x806\x03\x81\x01\x90a\0\x9F\x91\x90a\x1D^V[a\x01tV[\0[4\x80\x15a\0\xB1W__\xFD[Pa\0\xBAa\x06\xC8V[\0[4\x80\x15a\0\xC7W__\xFD[Pa\0\xD0a\x08xV[`@Qa\0\xDD\x91\x90a\x1D\xD1V[`@Q\x80\x91\x03\x90\xF3[4\x80\x15a\0\xF1W__\xFD[Pa\0\xFAa\x08\x9CV[\0[4\x80\x15a\x01\x07W__\xFD[Pa\x01\"`\x04\x806\x03\x81\x01\x90a\x01\x1D\x91\x90a\x1D^V[a\tuV[\0[4\x80\x15a\x01/W__\xFD[Pa\x01J`\x04\x806\x03\x81\x01\x90a\x01E\x91\x90a\x1D\xEAV[a\x12\x93V[\0[4\x80\x15a\x01WW__\xFD[Pa\x01r`\x04\x806\x03\x81\x01\x90a\x01m\x91\x90a\x1E\xA9V[a\x13\xBDV[\0[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x02\x02W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x01\xF9\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x02P\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x02kW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x02\x8F\x91\x90a\x1F\xA6V[\x90P_`\x01s\xFF\xFD\x89c\xEF\xD1\xFCjPd\x88I]\x95\x1DRc\x98\x8D&a\x02\xB3\x91\x90a\x1F\xFEV[\x90P_3\x90P_\x81\x88\x88s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xD8\x91,\x10h\x1D\x8B!\xFD7B$ODe\x8D\xBA\x12&N\x8A_`@Q` \x01a\x02\xFF\x97\x96\x95\x94\x93\x92\x91\x90a nV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x90P__\x88s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x12\x8A\xCB\x08\x8B_\x8B\x89\x88`@Q\x86c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x03S\x95\x94\x93\x92\x91\x90a!iV[`@\x80Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x03nW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x03\x92\x91\x90a!\xD5V[\x91P\x91P_\x82a\x03\xA1\x90a\"\x13V[\x90P__\x8Cs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\t\x02\xF1\xAC`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01```@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x03\xEEW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x04\x12\x91\x90a\"\xD5V[Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91P_a\x04C\x84\x84\x84a\x19\x1DV[\x90P\x8Ds\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x02,\r\x9F_\x830`@Q\x84c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x04\x82\x93\x92\x91\x90a#\x8AV[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x04\x99W__\xFD[PZ\xF1\x15\x80\x15a\x04\xABW=__>=_\xFD[PPPP_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x04\xFD\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x05\x18W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x05<\x91\x90a\x1F\xA6V[\x90P_\x8B\x82a\x05K\x91\x90a#\xD2V[\x90P_\x81\x13a\x05\x8FW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x05\x86\x90a$\\V[`@Q\x80\x91\x03\x90\xFD[_`d\x8E\x83a\x05\x9E\x91\x90a$zV[a\x05\xA8\x91\x90a$\xE8V[\x90Ps\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c.\x1A}M\x82`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x05\xF7\x91\x90a%\x18V[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x06\x0EW__\xFD[PZ\xF1\x15\x80\x15a\x06 W=__>=_\xFD[PPPPAs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x08\xFC\x82\x90\x81\x15\x02\x90`@Q_`@Q\x80\x83\x03\x81\x85\x88\x88\xF1\x93PPPP\x15\x80\x15a\x06gW=__>=_\xFD[P\x8C\x81\x84a\x06u\x91\x90a%1V[\x11a\x06\xB5W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x06\xAC\x90a%\xAEV[`@Q\x80\x91\x03\x90\xFD[PPPPPPPPPPPPPPPPPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x07VW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x07M\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x07\xA4\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x07\xBFW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x07\xE3\x91\x90a\x1F\xA6V[\x90Ps\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\xA9\x05\x9C\xBB3\x83`@Q\x83c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x084\x92\x91\x90a%\xCCV[` `@Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x08PW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x08t\x91\x90a&\x1DV[PPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x81V[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\t*W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\t!\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_G\x90P3s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x08\xFC\x82\x90\x81\x15\x02\x90`@Q_`@Q\x80\x83\x03\x81\x85\x88\x88\xF1\x93PPPP\x15\x80\x15a\tqW=__>=_\xFD[PPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\n\x03W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\t\xFA\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\nQ\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\nlW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\n\x90\x91\x90a\x1F\xA6V[\x90P_`\x01d\x01\0\x02v\xA3a\n\xA5\x91\x90a&HV[\x90P_3\x90P_\x81\x88\x88s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xD8\x91,\x10h\x1D\x8B!\xFD7B$ODe\x8D\xBA\x12&N\x8A`\x01`@Q` \x01a\n\xF2\x97\x96\x95\x94\x93\x92\x91\x90a nV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x90Pa\x0B$`@Q\x80``\x01`@R\x80`*\x81R` \x01a+H`*\x919a\x19rV[a\x0Bc`@Q\x80`@\x01`@R\x80`\r\x81R` \x01\x7F  v2Pool = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x89a\x1A\x0BV[a\x0B\xA3`@Q\x80`@\x01`@R\x80`\x11\x81R` \x01\x7F  zeroForOne = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP`\x01a\x1A\xA7V[a\x0B\xE2`@Q\x80`@\x01`@R\x80`\x0F\x81R` \x01\x7F  amountIn = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x87a\x1BCV[a\x0C7`@Q\x80`@\x01`@R\x80`\x18\x81R` \x01\x7F  sqrtPriceLimitX96 = %s\0\0\0\0\0\0\0\0\x81RP\x84s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x1BCV[__\x88s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x12\x8A\xCB\x08\x8B`\x01\x8B\x89\x88`@Q\x86c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x0C{\x95\x94\x93\x92\x91\x90a!iV[`@\x80Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x0C\x96W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x0C\xBA\x91\x90a!\xD5V[\x91P\x91Pa\x0C\xFC`@Q\x80`@\x01`@R\x80` \x81R` \x01\x7FUniswapV3 pool WETH/PLU changes:\x81RPa\x19rV[a\r\x1E`@Q\x80``\x01`@R\x80`+\x81R` \x01a*o`+\x919\x83a\x1B\xDFV[a\r@`@Q\x80``\x01`@R\x80`*\x81R` \x01a+r`*\x919\x82a\x1B\xDFV[__\x8Bs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\t\x02\xF1\xAC`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01```@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\r\x8BW=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\r\xAF\x91\x90a\"\xD5V[Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91Pm\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x91Pa\r\xF5`@Q\x80``\x01`@R\x80`$\x81R` \x01a*\x9A`$\x919a\x19rV[a\x0E4`@Q\x80`@\x01`@R\x80`\x1F\x81R` \x01\x7F  wethReserveV2 = %s wei (WETH)\0\x81RP\x83a\x1BCV[a\x0Es`@Q\x80`@\x01`@R\x80`\x1E\x81R` \x01\x7F  pluReserveV2  = %s wei (PLU)\0\0\x81RP\x82a\x1BCV[_\x83a\x0E~\x90a\"\x13V[\x90P_a\x0E\x8C\x82\x84\x86a\x19\x1DV[\x90Pa\x0E\xAF`@Q\x80`\x80\x01`@R\x80`E\x81R` \x01a*\xE0`E\x919a\x19rV[a\x0E\xEE`@Q\x80`@\x01`@R\x80`\x1E\x81R` \x01\x7F  pluAmountIn   = %s wei (PLU)\0\0\x81RP\x83a\x1BCV[a\x0F-`@Q\x80`@\x01`@R\x80`\x1F\x81R` \x01\x7F  wethAmountOut = %s wei (WETH)\0\x81RP\x82a\x1BCV[a\x0Fk`@Q\x80`@\x01`@R\x80`\x1D\x81R` \x01\x7FIUniswapV2Pair(v2Pool).swap()\0\0\0\x81RPa\x19rV[\x8Ds\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\x02,\r\x9F\x82_0`@Q\x84c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x0F\xA8\x93\x92\x91\x90a&\x8FV[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x0F\xBFW__\xFD[PZ\xF1\x15\x80\x15a\x0F\xD1W=__>=_\xFD[PPPP_s\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16cp\xA0\x8210`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x10#\x91\x90a\x1D\xD1V[` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x10>W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x10b\x91\x90a\x1F\xA6V[\x90P_\x8B\x82a\x10q\x91\x90a#\xD2V[\x90P_\x81\x13a\x10\xB5W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x10\xAC\x90a$\\V[`@Q\x80\x91\x03\x90\xFD[_`d\x8E\x83a\x10\xC4\x91\x90a$zV[a\x10\xCE\x91\x90a$\xE8V[\x90Ps\xC0*\xAA9\xB2#\xFE\x8D\n\x0E\\O'\xEA\xD9\x08<ul\xC2s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c.\x1A}M\x82`@Q\x82c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x11\x1D\x91\x90a%\x18V[_`@Q\x80\x83\x03\x81_\x87\x80;\x15\x80\x15a\x114W__\xFD[PZ\xF1\x15\x80\x15a\x11FW=__>=_\xFD[PPPPAs\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16a\x08\xFC\x82\x90\x81\x15\x02\x90`@Q_`@Q\x80\x83\x03\x81\x85\x88\x88\xF1\x93PPPP\x15\x80\x15a\x11\x8DW=__>=_\xFD[Pa\x11\xB0`@Q\x80``\x01`@R\x80`#\x81R` \x01a+%`#\x919\x8Ea\x1BCV[a\x11\xD2`@Q\x80``\x01`@R\x80`\"\x81R` \x01a*M`\"\x919\x84a\x1BCV[a\x11\xF4`@Q\x80``\x01`@R\x80`\"\x81R` \x01a*\xBE`\"\x919\x82a\x1BCV[a\x123`@Q\x80`@\x01`@R\x80`\x18\x81R` \x01\x7F  profit = %s wei (WETH)\0\0\0\0\0\0\0\0\x81RP\x83a\x1B\xDFV[\x8C\x81\x84a\x12@\x91\x90a%1V[\x11a\x12\x80W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x12w\x90a%\xAEV[`@Q\x80\x91\x03\x90\xFD[PPPPPPPPPPPPPPPPPV[__\x90T\x90a\x01\0\n\x90\x04s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x13!W`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x13\x18\x90a\x1FtV[`@Q\x80\x91\x03\x90\xFD[\x80__a\x01\0\n\x81T\x81s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x02\x19\x16\x90\x83s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x02\x17\x90UP\x80s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x7F\x8B\xE0\x07\x9CS\x16Y\x14\x13D\xCD\x1F\xD0\xA4\xF2\x84\x19I\x7F\x97\"\xA3\xDA\xAF\xE3\xB4\x18okdW\xE0`@Q`@Q\x80\x91\x03\x90\xA3PV[a\x13\xFB`@Q\x80`@\x01`@R\x80`\x17\x81R` \x01\x7FuniswapV3SwapCallback()\0\0\0\0\0\0\0\0\0\x81RPa\x19rV[a\x14:`@Q\x80`@\x01`@R\x80`\x13\x81R` \x01\x7F  amount0Delta = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x85a\x1B\xDFV[a\x14y`@Q\x80`@\x01`@R\x80`\x13\x81R` \x01\x7F  amount1Delta = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x84a\x1B\xDFV[_\x84\x13\x80\x15a\x14\x87WP_\x83\x12[\x80a\x14\x9CWP_\x84\x12\x80\x15a\x14\x9BWP_\x83\x13[[a\x14\xDBW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x14\xD2\x90a'!V[`@Q\x80\x91\x03\x90\xFD[_______\x88\x88\x81\x01\x90a\x14\xF1\x91\x90a'\x8EV[\x96P\x96P\x96P\x96P\x96P\x96P\x96P\x84s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x163s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16\x14a\x15mW`@Q\x7F\x08\xC3y\xA0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81R`\x04\x01a\x15d\x90a(uV[`@Q\x80\x91\x03\x90\xFD[a\x15\xAB`@Q\x80`@\x01`@R\x80`\x16\x81R` \x01\x7FDecoded callback data:\0\0\0\0\0\0\0\0\0\0\x81RPa\x19rV[a\x15\xEA`@Q\x80`@\x01`@R\x80`\x12\x81R` \x01\x7F  beneficiary = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x88a\x1A\x0BV[a\x16)`@Q\x80`@\x01`@R\x80`\r\x81R` \x01\x7F  v2Pool = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x87a\x1A\x0BV[a\x16h`@Q\x80`@\x01`@R\x80`\r\x81R` \x01\x7F  v3Pool = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x86a\x1A\x0BV[a\x16\xA7`@Q\x80`@\x01`@R\x80`\x0E\x81R` \x01\x7F  tokenIn = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x85a\x1A\x0BV[a\x16\xE6`@Q\x80`@\x01`@R\x80`\x0F\x81R` \x01\x7F  tokenOut = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x84a\x1A\x0BV[a\x17%`@Q\x80`@\x01`@R\x80`\x0F\x81R` \x01\x7F  amountIn = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x83a\x1BCV[a\x17d`@Q\x80`@\x01`@R\x80`\x11\x81R` \x01\x7F  zeroForOne = %s\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x81RP\x82a\x1A\xA7V[___\x8D\x13a\x17\xE0W\x86s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\xD2\x12 \xA7`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x17\xB6W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x17\xDA\x91\x90a(\xA7V[\x8Ca\x18OV[\x86s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\r\xFE\x16\x81`@Q\x81c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01` `@Q\x80\x83\x03\x81\x86Z\xFA\x15\x80\x15a\x18)W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x18M\x91\x90a(\xA7V[\x8D[\x91P\x91Pa\x18\x92`@Q\x80`@\x01`@R\x80`\x1D\x81R` \x01\x7FamountToRepay = %s wei (WETH)\0\0\0\x81RP\x82a\x1BCV[\x81s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x16c\xA9\x05\x9C\xBB\x88\x83`@Q\x83c\xFF\xFF\xFF\xFF\x16`\xE0\x1B\x81R`\x04\x01a\x18\xCD\x92\x91\x90a%\xCCV[` `@Q\x80\x83\x03\x81_\x87Z\xF1\x15\x80\x15a\x18\xE9W=__>=_\xFD[PPPP`@Q=`\x1F\x19`\x1F\x82\x01\x16\x82\x01\x80`@RP\x81\x01\x90a\x19\r\x91\x90a&\x1DV[PPPPPPPPPPPPPPV[__a\x03\xE5\x85a\x19-\x91\x90a$zV[\x90P_\x83\x82a\x19<\x91\x90a$zV[\x90P_\x82a\x03\xE8\x87a\x19N\x91\x90a$zV[a\x19X\x91\x90a(\xD2V[\x90P\x80\x82a\x19f\x91\x90a$\xE8V[\x93PPPP\x93\x92PPPV[a\x1A\x08\x81`@Q`$\x01a\x19\x86\x91\x90a)GV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7FA0O\xAC\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PV[a\x1A\xA3\x82\x82`@Q`$\x01a\x1A!\x92\x91\x90a)gV[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F1\x9A\xF33\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1B?\x82\x82`@Q`$\x01a\x1A\xBD\x92\x91\x90a)\x95V[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F\xC3\xB5V5\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1B\xDB\x82\x82`@Q`$\x01a\x1BY\x92\x91\x90a)\xC3V[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F\xB6\x0Er\xCC\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1Cw\x82\x82`@Q`$\x01a\x1B\xF5\x92\x91\x90a)\xF1V[`@Q` \x81\x83\x03\x03\x81R\x90`@R\x7F<\xA6&\x8E\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x19\x16` \x82\x01\x80Q{\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x83\x81\x83\x16\x17\x83RPPPPa\x1C{V[PPV[a\x1C\x92\x81a\x1C\x8Aa\x1C\x95a\x1C\xB4V[c\xFF\xFF\xFF\xFF\x16V[PV[_jconsole.log\x90P__\x83Q` \x85\x01\x84Z\xFAPPPV[a\x1C\xBF\x81\x90P\x91\x90PV[a\x1C\xC7a*\x1FV[V[__\xFD[__\xFD[_s\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x82\x16\x90P\x91\x90PV[_a\x1C\xFA\x82a\x1C\xD1V[\x90P\x91\x90PV[a\x1D\n\x81a\x1C\xF0V[\x81\x14a\x1D\x14W__\xFD[PV[_\x815\x90Pa\x1D%\x81a\x1D\x01V[\x92\x91PPV[_\x81\x90P\x91\x90PV[a\x1D=\x81a\x1D+V[\x81\x14a\x1DGW__\xFD[PV[_\x815\x90Pa\x1DX\x81a\x1D4V[\x92\x91PPV[____`\x80\x85\x87\x03\x12\x15a\x1DvWa\x1Dua\x1C\xC9V[[_a\x1D\x83\x87\x82\x88\x01a\x1D\x17V[\x94PP` a\x1D\x94\x87\x82\x88\x01a\x1D\x17V[\x93PP`@a\x1D\xA5\x87\x82\x88\x01a\x1DJV[\x92PP``a\x1D\xB6\x87\x82\x88\x01a\x1DJV[\x91PP\x92\x95\x91\x94P\x92PV[a\x1D\xCB\x81a\x1C\xF0V[\x82RPPV[_` \x82\x01\x90Pa\x1D\xE4_\x83\x01\x84a\x1D\xC2V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a\x1D\xFFWa\x1D\xFEa\x1C\xC9V[[_a\x1E\x0C\x84\x82\x85\x01a\x1D\x17V[\x91PP\x92\x91PPV[_\x81\x90P\x91\x90PV[a\x1E'\x81a\x1E\x15V[\x81\x14a\x1E1W__\xFD[PV[_\x815\x90Pa\x1EB\x81a\x1E\x1EV[\x92\x91PPV[__\xFD[__\xFD[__\xFD[__\x83`\x1F\x84\x01\x12a\x1EiWa\x1Eha\x1EHV[[\x825\x90Pg\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a\x1E\x86Wa\x1E\x85a\x1ELV[[` \x83\x01\x91P\x83`\x01\x82\x02\x83\x01\x11\x15a\x1E\xA2Wa\x1E\xA1a\x1EPV[[\x92P\x92\x90PV[____``\x85\x87\x03\x12\x15a\x1E\xC1Wa\x1E\xC0a\x1C\xC9V[[_a\x1E\xCE\x87\x82\x88\x01a\x1E4V[\x94PP` a\x1E\xDF\x87\x82\x88\x01a\x1E4V[\x93PP`@\x85\x015g\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a\x1F\0Wa\x1E\xFFa\x1C\xCDV[[a\x1F\x0C\x87\x82\x88\x01a\x1ETV[\x92P\x92PP\x92\x95\x91\x94P\x92PV[_\x82\x82R` \x82\x01\x90P\x92\x91PPV[\x7FUNAUTHORIZED\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a\x1F^`\x0C\x83a\x1F\x1AV[\x91Pa\x1Fi\x82a\x1F*V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra\x1F\x8B\x81a\x1FRV[\x90P\x91\x90PV[_\x81Q\x90Pa\x1F\xA0\x81a\x1D4V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a\x1F\xBBWa\x1F\xBAa\x1C\xC9V[[_a\x1F\xC8\x84\x82\x85\x01a\x1F\x92V[\x91PP\x92\x91PPV[\x7FNH{q\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_R`\x11`\x04R`$_\xFD[_a \x08\x82a\x1C\xD1V[\x91Pa \x13\x83a\x1C\xD1V[\x92P\x82\x82\x03\x90Ps\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a ?Wa >a\x1F\xD1V[[\x92\x91PPV[a N\x81a\x1D+V[\x82RPPV[_\x81\x15\x15\x90P\x91\x90PV[a h\x81a TV[\x82RPPV[_`\xE0\x82\x01\x90Pa \x81_\x83\x01\x8Aa\x1D\xC2V[a \x8E` \x83\x01\x89a\x1D\xC2V[a \x9B`@\x83\x01\x88a\x1D\xC2V[a \xA8``\x83\x01\x87a\x1D\xC2V[a \xB5`\x80\x83\x01\x86a\x1D\xC2V[a \xC2`\xA0\x83\x01\x85a EV[a \xCF`\xC0\x83\x01\x84a _V[\x98\x97PPPPPPPPV[a \xE4\x81a\x1E\x15V[\x82RPPV[a \xF3\x81a\x1C\xD1V[\x82RPPV[_\x81Q\x90P\x91\x90PV[_\x82\x82R` \x82\x01\x90P\x92\x91PPV[\x82\x81\x83^_\x83\x83\x01RPPPV[_`\x1F\x19`\x1F\x83\x01\x16\x90P\x91\x90PV[_a!;\x82a \xF9V[a!E\x81\x85a!\x03V[\x93Pa!U\x81\x85` \x86\x01a!\x13V[a!^\x81a!!V[\x84\x01\x91PP\x92\x91PPV[_`\xA0\x82\x01\x90Pa!|_\x83\x01\x88a\x1D\xC2V[a!\x89` \x83\x01\x87a _V[a!\x96`@\x83\x01\x86a \xDBV[a!\xA3``\x83\x01\x85a \xEAV[\x81\x81\x03`\x80\x83\x01Ra!\xB5\x81\x84a!1V[\x90P\x96\x95PPPPPPV[_\x81Q\x90Pa!\xCF\x81a\x1E\x1EV[\x92\x91PPV[__`@\x83\x85\x03\x12\x15a!\xEBWa!\xEAa\x1C\xC9V[[_a!\xF8\x85\x82\x86\x01a!\xC1V[\x92PP` a\"\t\x85\x82\x86\x01a!\xC1V[\x91PP\x92P\x92\x90PV[_a\"\x1D\x82a\x1E\x15V[\x91P\x7F\x80\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x82\x03a\"OWa\"Na\x1F\xD1V[[\x81_\x03\x90P\x91\x90PV[_m\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x82\x16\x90P\x91\x90PV[a\"{\x81a\"YV[\x81\x14a\"\x85W__\xFD[PV[_\x81Q\x90Pa\"\x96\x81a\"rV[\x92\x91PPV[_c\xFF\xFF\xFF\xFF\x82\x16\x90P\x91\x90PV[a\"\xB4\x81a\"\x9CV[\x81\x14a\"\xBEW__\xFD[PV[_\x81Q\x90Pa\"\xCF\x81a\"\xABV[\x92\x91PPV[___``\x84\x86\x03\x12\x15a\"\xECWa\"\xEBa\x1C\xC9V[[_a\"\xF9\x86\x82\x87\x01a\"\x88V[\x93PP` a#\n\x86\x82\x87\x01a\"\x88V[\x92PP`@a#\x1B\x86\x82\x87\x01a\"\xC1V[\x91PP\x92P\x92P\x92V[_\x81\x90P\x91\x90PV[_\x81\x90P\x91\x90PV[_a#Qa#La#G\x84a#%V[a#.V[a\x1D+V[\x90P\x91\x90PV[a#a\x81a#7V[\x82RPPV[PV[_a#u_\x83a!\x03V[\x91Pa#\x80\x82a#gV[_\x82\x01\x90P\x91\x90PV[_`\x80\x82\x01\x90Pa#\x9D_\x83\x01\x86a#XV[a#\xAA` \x83\x01\x85a EV[a#\xB7`@\x83\x01\x84a\x1D\xC2V[\x81\x81\x03``\x83\x01Ra#\xC8\x81a#jV[\x90P\x94\x93PPPPV[_a#\xDC\x82a\x1E\x15V[\x91Pa#\xE7\x83a\x1E\x15V[\x92P\x82\x82\x03\x90P\x81\x81\x12_\x84\x12\x16\x82\x82\x13_\x85\x12\x15\x16\x17\x15a$\x0CWa$\x0Ba\x1F\xD1V[[\x92\x91PPV[\x7Fno profit\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a$F`\t\x83a\x1F\x1AV[\x91Pa$Q\x82a$\x12V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra$s\x81a$:V[\x90P\x91\x90PV[_a$\x84\x82a\x1D+V[\x91Pa$\x8F\x83a\x1D+V[\x92P\x82\x82\x02a$\x9D\x81a\x1D+V[\x91P\x82\x82\x04\x84\x14\x83\x15\x17a$\xB4Wa$\xB3a\x1F\xD1V[[P\x92\x91PPV[\x7FNH{q\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_R`\x12`\x04R`$_\xFD[_a$\xF2\x82a\x1D+V[\x91Pa$\xFD\x83a\x1D+V[\x92P\x82a%\rWa%\x0Ca$\xBBV[[\x82\x82\x04\x90P\x92\x91PPV[_` \x82\x01\x90Pa%+_\x83\x01\x84a EV[\x92\x91PPV[_a%;\x82a\x1D+V[\x91Pa%F\x83a\x1D+V[\x92P\x82\x82\x03\x90P\x81\x81\x11\x15a%^Wa%]a\x1F\xD1V[[\x92\x91PPV[\x7Farbitrage failed\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a%\x98`\x10\x83a\x1F\x1AV[\x91Pa%\xA3\x82a%dV[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra%\xC5\x81a%\x8CV[\x90P\x91\x90PV[_`@\x82\x01\x90Pa%\xDF_\x83\x01\x85a\x1D\xC2V[a%\xEC` \x83\x01\x84a EV[\x93\x92PPPV[a%\xFC\x81a TV[\x81\x14a&\x06W__\xFD[PV[_\x81Q\x90Pa&\x17\x81a%\xF3V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a&2Wa&1a\x1C\xC9V[[_a&?\x84\x82\x85\x01a&\tV[\x91PP\x92\x91PPV[_a&R\x82a\x1C\xD1V[\x91Pa&]\x83a\x1C\xD1V[\x92P\x82\x82\x01\x90Ps\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF\x81\x11\x15a&\x89Wa&\x88a\x1F\xD1V[[\x92\x91PPV[_`\x80\x82\x01\x90Pa&\xA2_\x83\x01\x86a EV[a&\xAF` \x83\x01\x85a#XV[a&\xBC`@\x83\x01\x84a\x1D\xC2V[\x81\x81\x03``\x83\x01Ra&\xCD\x81a#jV[\x90P\x94\x93PPPPV[\x7Fnothing to repay\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a'\x0B`\x10\x83a\x1F\x1AV[\x91Pa'\x16\x82a&\xD7V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra'8\x81a&\xFFV[\x90P\x91\x90PV[_a'I\x82a\x1C\xD1V[\x90P\x91\x90PV[a'Y\x81a'?V[\x81\x14a'cW__\xFD[PV[_\x815\x90Pa't\x81a'PV[\x92\x91PPV[_\x815\x90Pa'\x88\x81a%\xF3V[\x92\x91PPV[_______`\xE0\x88\x8A\x03\x12\x15a'\xA9Wa'\xA8a\x1C\xC9V[[_a'\xB6\x8A\x82\x8B\x01a'fV[\x97PP` a'\xC7\x8A\x82\x8B\x01a'fV[\x96PP`@a'\xD8\x8A\x82\x8B\x01a'fV[\x95PP``a'\xE9\x8A\x82\x8B\x01a'fV[\x94PP`\x80a'\xFA\x8A\x82\x8B\x01a'fV[\x93PP`\xA0a(\x0B\x8A\x82\x8B\x01a\x1DJV[\x92PP`\xC0a(\x1C\x8A\x82\x8B\x01a'zV[\x91PP\x92\x95\x98\x91\x94\x97P\x92\x95PV[\x7Finvalid sender\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x82\x01RPV[_a(_`\x0E\x83a\x1F\x1AV[\x91Pa(j\x82a(+V[` \x82\x01\x90P\x91\x90PV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra(\x8C\x81a(SV[\x90P\x91\x90PV[_\x81Q\x90Pa(\xA1\x81a\x1D\x01V[\x92\x91PPV[_` \x82\x84\x03\x12\x15a(\xBCWa(\xBBa\x1C\xC9V[[_a(\xC9\x84\x82\x85\x01a(\x93V[\x91PP\x92\x91PPV[_a(\xDC\x82a\x1D+V[\x91Pa(\xE7\x83a\x1D+V[\x92P\x82\x82\x01\x90P\x80\x82\x11\x15a(\xFFWa(\xFEa\x1F\xD1V[[\x92\x91PPV[_\x81Q\x90P\x91\x90PV[_a)\x19\x82a)\x05V[a)#\x81\x85a\x1F\x1AV[\x93Pa)3\x81\x85` \x86\x01a!\x13V[a)<\x81a!!V[\x84\x01\x91PP\x92\x91PPV[_` \x82\x01\x90P\x81\x81\x03_\x83\x01Ra)_\x81\x84a)\x0FV[\x90P\x92\x91PPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra)\x7F\x81\x85a)\x0FV[\x90Pa)\x8E` \x83\x01\x84a\x1D\xC2V[\x93\x92PPPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra)\xAD\x81\x85a)\x0FV[\x90Pa)\xBC` \x83\x01\x84a _V[\x93\x92PPPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra)\xDB\x81\x85a)\x0FV[\x90Pa)\xEA` \x83\x01\x84a EV[\x93\x92PPPV[_`@\x82\x01\x90P\x81\x81\x03_\x83\x01Ra*\t\x81\x85a)\x0FV[\x90Pa*\x18` \x83\x01\x84a \xDBV[\x93\x92PPPV[\x7FNH{q\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_R`Q`\x04R`$_\xFD\xFE  wethBalanceAfter = %s wei (WETH)  wethAmountDelta (amount0) = %s wei (WETH)IUniswapV2Pair(v2Pool).getReserves()  profitToCoinbase = %s wei (WETH)uniswapV2CalculateAmountOut(pluAmountIn, pluReserveV2, wethReserveV2)  wethBalanceBefore = %s wei (WETH)IUniswapV3Pool(v3Pool).swap [WETH -> PLU]:  pluAmountDelta (amount1)  = %s wei (PLU)\xA2dipfsX\"\x12 \xC38\x10\x82\x1C\xB2\xB1` \xF6*P\xB4\x1C^A\xE4\xFC\xD6\xA0\xE8iW\xEC\x9B\x90\x8B\x91s*\xF9\x92dsolcC\0\x08\x1E\x003",
    );
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Event with signature `OwnershipTransferred(address,address)` and selector `0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0`.
    ```solidity
    event OwnershipTransferred(address indexed user, address indexed newOwner);
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    #[derive(Clone)]
    pub struct OwnershipTransferred {
        #[allow(missing_docs)]
        pub user: alloy::sol_types::private::Address,
        #[allow(missing_docs)]
        pub newOwner: alloy::sol_types::private::Address,
    }
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        #[automatically_derived]
        impl alloy_sol_types::SolEvent for OwnershipTransferred {
            type DataTuple<'a> = ();
            type DataToken<'a> =
                <Self::DataTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            type TopicList = (
                alloy_sol_types::sol_data::FixedBytes<32>,
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Address,
            );
            const SIGNATURE: &'static str =
                "OwnershipTransferred(address,address)";
            const SIGNATURE_HASH: alloy_sol_types::private::B256 =
                alloy_sol_types::private::B256::new([
                    139u8, 224u8, 7u8, 156u8, 83u8, 22u8, 89u8, 20u8, 19u8,
                    68u8, 205u8, 31u8, 208u8, 164u8, 242u8, 132u8, 25u8, 73u8,
                    127u8, 151u8, 34u8, 163u8, 218u8, 175u8, 227u8, 180u8,
                    24u8, 111u8, 107u8, 100u8, 87u8, 224u8,
                ]);
            const ANONYMOUS: bool = false;
            #[allow(unused_variables)]
            #[inline]
            fn new(
                topics: <Self::TopicList as alloy_sol_types::SolType>::RustType,
                data: <Self::DataTuple<'_> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                Self {
                    user: topics.1,
                    newOwner: topics.2,
                }
            }
            #[inline]
            fn check_signature(
                topics: &<Self::TopicList as alloy_sol_types::SolType>::RustType,
            ) -> alloy_sol_types::Result<()> {
                if topics.0 != Self::SIGNATURE_HASH {
                    return Err(
                        alloy_sol_types::Error::invalid_event_signature_hash(
                            Self::SIGNATURE,
                            topics.0,
                            Self::SIGNATURE_HASH,
                        ),
                    );
                }
                Ok(())
            }
            #[inline]
            fn tokenize_body(&self) -> Self::DataToken<'_> {
                ()
            }
            #[inline]
            fn topics(
                &self,
            ) -> <Self::TopicList as alloy_sol_types::SolType>::RustType
            {
                (
                    Self::SIGNATURE_HASH.into(),
                    self.user.clone(),
                    self.newOwner.clone(),
                )
            }
            #[inline]
            fn encode_topics_raw(
                &self,
                out: &mut [alloy_sol_types::abi::token::WordToken],
            ) -> alloy_sol_types::Result<()> {
                if out.len()
                    < <Self::TopicList as alloy_sol_types::TopicList>::COUNT
                {
                    return Err(alloy_sol_types::Error::Overrun);
                }
                out[0usize] = alloy_sol_types::abi::token::WordToken(
                    Self::SIGNATURE_HASH,
                );
                out[1usize] = <alloy::sol_types::sol_data::Address as alloy_sol_types::EventTopic>::encode_topic(
                    &self.user,
                );
                out[2usize] = <alloy::sol_types::sol_data::Address as alloy_sol_types::EventTopic>::encode_topic(
                    &self.newOwner,
                );
                Ok(())
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::private::IntoLogData for OwnershipTransferred {
            fn to_log_data(&self) -> alloy_sol_types::private::LogData {
                From::from(self)
            }
            fn into_log_data(self) -> alloy_sol_types::private::LogData {
                From::from(&self)
            }
        }
        #[automatically_derived]
        impl From<&OwnershipTransferred> for alloy_sol_types::private::LogData {
            #[inline]
            fn from(
                this: &OwnershipTransferred,
            ) -> alloy_sol_types::private::LogData {
                alloy_sol_types::SolEvent::encode_log_data(this)
            }
        }
    };
    /**Constructor`.
    ```solidity
    constructor();
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct constructorCall {}
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<constructorCall> for UnderlyingRustTuple<'_> {
                fn from(value: constructorCall) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for constructorCall {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolConstructor for constructorCall {
            type Parameters<'a> = ();
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                ()
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `execute_weth_token0(address,address,uint256,uint256)` and selector `0xed34b839`.
    ```solidity
    function execute_weth_token0(address v2Pool, address v3Pool, uint256 amountIn, uint256 percentageToPayToCoinbase) external;
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct execute_weth_token0Call {
        #[allow(missing_docs)]
        pub v2Pool: alloy::sol_types::private::Address,
        #[allow(missing_docs)]
        pub v3Pool: alloy::sol_types::private::Address,
        #[allow(missing_docs)]
        pub amountIn: alloy::sol_types::private::primitives::aliases::U256,
        #[allow(missing_docs)]
        pub percentageToPayToCoinbase:
            alloy::sol_types::private::primitives::aliases::U256,
    }
    ///Container type for the return parameters of the
    /// [`execute_weth_token0(address,address,uint256,
    /// uint256)`](execute_weth_token0Call) function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct execute_weth_token0Return {}
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = (
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Uint<256>,
                alloy::sol_types::sol_data::Uint<256>,
            );
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = (
                alloy::sol_types::private::Address,
                alloy::sol_types::private::Address,
                alloy::sol_types::private::primitives::aliases::U256,
                alloy::sol_types::private::primitives::aliases::U256,
            );
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<execute_weth_token0Call>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: execute_weth_token0Call) -> Self {
                    (
                        value.v2Pool,
                        value.v3Pool,
                        value.amountIn,
                        value.percentageToPayToCoinbase,
                    )
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for execute_weth_token0Call
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {
                        v2Pool: tuple.0,
                        v3Pool: tuple.1,
                        amountIn: tuple.2,
                        percentageToPayToCoinbase: tuple.3,
                    }
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<execute_weth_token0Return>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: execute_weth_token0Return) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for execute_weth_token0Return
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        impl execute_weth_token0Return {
            fn _tokenize(
                &self,
            ) -> <execute_weth_token0Call as alloy_sol_types::SolCall>::ReturnToken<'_>{
                ()
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for execute_weth_token0Call {
            type Parameters<'a> = (
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Uint<256>,
                alloy::sol_types::sol_data::Uint<256>,
            );
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = execute_weth_token0Return;
            type ReturnTuple<'a> = ();
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str =
                "execute_weth_token0(address,address,uint256,uint256)";
            const SELECTOR: [u8; 4] = [237u8, 52u8, 184u8, 57u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                (
                    <alloy::sol_types::sol_data::Address as alloy_sol_types::SolType>::tokenize(
                        &self.v2Pool,
                    ),
                    <alloy::sol_types::sol_data::Address as alloy_sol_types::SolType>::tokenize(
                        &self.v3Pool,
                    ),
                    <alloy::sol_types::sol_data::Uint<
                        256,
                    > as alloy_sol_types::SolType>::tokenize(&self.amountIn),
                    <alloy::sol_types::sol_data::Uint<
                        256,
                    > as alloy_sol_types::SolType>::tokenize(
                        &self.percentageToPayToCoinbase,
                    ),
                )
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                execute_weth_token0Return::_tokenize(ret)
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(Into::into)
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(Into::into)
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `execute_weth_token1(address,address,uint256,uint256)` and selector `0x253b21cd`.
    ```solidity
    function execute_weth_token1(address v2Pool, address v3Pool, uint256 amountIn, uint256 percentageToPayToCoinbase) external;
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct execute_weth_token1Call {
        #[allow(missing_docs)]
        pub v2Pool: alloy::sol_types::private::Address,
        #[allow(missing_docs)]
        pub v3Pool: alloy::sol_types::private::Address,
        #[allow(missing_docs)]
        pub amountIn: alloy::sol_types::private::primitives::aliases::U256,
        #[allow(missing_docs)]
        pub percentageToPayToCoinbase:
            alloy::sol_types::private::primitives::aliases::U256,
    }
    ///Container type for the return parameters of the
    /// [`execute_weth_token1(address,address,uint256,
    /// uint256)`](execute_weth_token1Call) function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct execute_weth_token1Return {}
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = (
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Uint<256>,
                alloy::sol_types::sol_data::Uint<256>,
            );
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = (
                alloy::sol_types::private::Address,
                alloy::sol_types::private::Address,
                alloy::sol_types::private::primitives::aliases::U256,
                alloy::sol_types::private::primitives::aliases::U256,
            );
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<execute_weth_token1Call>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: execute_weth_token1Call) -> Self {
                    (
                        value.v2Pool,
                        value.v3Pool,
                        value.amountIn,
                        value.percentageToPayToCoinbase,
                    )
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for execute_weth_token1Call
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {
                        v2Pool: tuple.0,
                        v3Pool: tuple.1,
                        amountIn: tuple.2,
                        percentageToPayToCoinbase: tuple.3,
                    }
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<execute_weth_token1Return>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: execute_weth_token1Return) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for execute_weth_token1Return
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        impl execute_weth_token1Return {
            fn _tokenize(
                &self,
            ) -> <execute_weth_token1Call as alloy_sol_types::SolCall>::ReturnToken<'_>{
                ()
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for execute_weth_token1Call {
            type Parameters<'a> = (
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Address,
                alloy::sol_types::sol_data::Uint<256>,
                alloy::sol_types::sol_data::Uint<256>,
            );
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = execute_weth_token1Return;
            type ReturnTuple<'a> = ();
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str =
                "execute_weth_token1(address,address,uint256,uint256)";
            const SELECTOR: [u8; 4] = [37u8, 59u8, 33u8, 205u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                (
                    <alloy::sol_types::sol_data::Address as alloy_sol_types::SolType>::tokenize(
                        &self.v2Pool,
                    ),
                    <alloy::sol_types::sol_data::Address as alloy_sol_types::SolType>::tokenize(
                        &self.v3Pool,
                    ),
                    <alloy::sol_types::sol_data::Uint<
                        256,
                    > as alloy_sol_types::SolType>::tokenize(&self.amountIn),
                    <alloy::sol_types::sol_data::Uint<
                        256,
                    > as alloy_sol_types::SolType>::tokenize(
                        &self.percentageToPayToCoinbase,
                    ),
                )
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                execute_weth_token1Return::_tokenize(ret)
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(Into::into)
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(Into::into)
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `owner()` and selector `0x8da5cb5b`.
    ```solidity
    function owner() external view returns (address);
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct ownerCall;
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    ///Container type for the return parameters of the [`owner()`](ownerCall)
    /// function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct ownerReturn {
        #[allow(missing_docs)]
        pub _0: alloy::sol_types::private::Address,
    }
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<ownerCall> for UnderlyingRustTuple<'_> {
                fn from(value: ownerCall) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for ownerCall {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> =
                (alloy::sol_types::sol_data::Address,);
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> =
                (alloy::sol_types::private::Address,);
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<ownerReturn> for UnderlyingRustTuple<'_> {
                fn from(value: ownerReturn) -> Self {
                    (value._0,)
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for ownerReturn {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self { _0: tuple.0 }
                }
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for ownerCall {
            type Parameters<'a> = ();
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = alloy::sol_types::private::Address;
            type ReturnTuple<'a> = (alloy::sol_types::sol_data::Address,);
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str = "owner()";
            const SELECTOR: [u8; 4] = [141u8, 165u8, 203u8, 91u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                ()
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                (
                    <alloy::sol_types::sol_data::Address as alloy_sol_types::SolType>::tokenize(
                        ret,
                    ),
                )
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(|r| {
                        let r: ownerReturn = r.into();
                        r._0
                    })
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(|r| {
                        let r: ownerReturn = r.into();
                        r._0
                    })
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `transferOwnership(address)` and selector `0xf2fde38b`.
    ```solidity
    function transferOwnership(address newOwner) external;
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct transferOwnershipCall {
        #[allow(missing_docs)]
        pub newOwner: alloy::sol_types::private::Address,
    }
    ///Container type for the return parameters of the
    /// [`transferOwnership(address)`](transferOwnershipCall) function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct transferOwnershipReturn {}
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> =
                (alloy::sol_types::sol_data::Address,);
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> =
                (alloy::sol_types::private::Address,);
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<transferOwnershipCall> for UnderlyingRustTuple<'_> {
                fn from(value: transferOwnershipCall) -> Self {
                    (value.newOwner,)
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for transferOwnershipCall {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self { newOwner: tuple.0 }
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<transferOwnershipReturn>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: transferOwnershipReturn) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for transferOwnershipReturn
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        impl transferOwnershipReturn {
            fn _tokenize(
                &self,
            ) -> <transferOwnershipCall as alloy_sol_types::SolCall>::ReturnToken<'_>{
                ()
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for transferOwnershipCall {
            type Parameters<'a> = (alloy::sol_types::sol_data::Address,);
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = transferOwnershipReturn;
            type ReturnTuple<'a> = ();
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str = "transferOwnership(address)";
            const SELECTOR: [u8; 4] = [242u8, 253u8, 227u8, 139u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                (
                    <alloy::sol_types::sol_data::Address as alloy_sol_types::SolType>::tokenize(
                        &self.newOwner,
                    ),
                )
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                transferOwnershipReturn::_tokenize(ret)
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(Into::into)
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(Into::into)
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `uniswapV3SwapCallback(int256,int256,bytes)` and selector `0xfa461e33`.
    ```solidity
    function uniswapV3SwapCallback(int256 amount0Delta, int256 amount1Delta, bytes memory data) external;
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct uniswapV3SwapCallbackCall {
        #[allow(missing_docs)]
        pub amount0Delta: alloy::sol_types::private::primitives::aliases::I256,
        #[allow(missing_docs)]
        pub amount1Delta: alloy::sol_types::private::primitives::aliases::I256,
        #[allow(missing_docs)]
        pub data: alloy::sol_types::private::Bytes,
    }
    ///Container type for the return parameters of the
    /// [`uniswapV3SwapCallback(int256,int256,
    /// bytes)`](uniswapV3SwapCallbackCall) function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct uniswapV3SwapCallbackReturn {}
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = (
                alloy::sol_types::sol_data::Int<256>,
                alloy::sol_types::sol_data::Int<256>,
                alloy::sol_types::sol_data::Bytes,
            );
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = (
                alloy::sol_types::private::primitives::aliases::I256,
                alloy::sol_types::private::primitives::aliases::I256,
                alloy::sol_types::private::Bytes,
            );
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<uniswapV3SwapCallbackCall>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: uniswapV3SwapCallbackCall) -> Self {
                    (
                        value.amount0Delta,
                        value.amount1Delta,
                        value.data,
                    )
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for uniswapV3SwapCallbackCall
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {
                        amount0Delta: tuple.0,
                        amount1Delta: tuple.1,
                        data: tuple.2,
                    }
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<uniswapV3SwapCallbackReturn>
                for UnderlyingRustTuple<'_>
            {
                fn from(value: uniswapV3SwapCallbackReturn) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>>
                for uniswapV3SwapCallbackReturn
            {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        impl uniswapV3SwapCallbackReturn {
            fn _tokenize(
                &self,
            ) -> <uniswapV3SwapCallbackCall as alloy_sol_types::SolCall>::ReturnToken<
                '_,
            >{
                ()
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for uniswapV3SwapCallbackCall {
            type Parameters<'a> = (
                alloy::sol_types::sol_data::Int<256>,
                alloy::sol_types::sol_data::Int<256>,
                alloy::sol_types::sol_data::Bytes,
            );
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = uniswapV3SwapCallbackReturn;
            type ReturnTuple<'a> = ();
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str =
                "uniswapV3SwapCallback(int256,int256,bytes)";
            const SELECTOR: [u8; 4] = [250u8, 70u8, 30u8, 51u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                (
                    <alloy::sol_types::sol_data::Int<
                        256,
                    > as alloy_sol_types::SolType>::tokenize(&self.amount0Delta),
                    <alloy::sol_types::sol_data::Int<
                        256,
                    > as alloy_sol_types::SolType>::tokenize(&self.amount1Delta),
                    <alloy::sol_types::sol_data::Bytes as alloy_sol_types::SolType>::tokenize(
                        &self.data,
                    ),
                )
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                uniswapV3SwapCallbackReturn::_tokenize(ret)
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(Into::into)
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(Into::into)
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `withdrawETH()` and selector `0xe086e5ec`.
    ```solidity
    function withdrawETH() external;
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct withdrawETHCall;
    ///Container type for the return parameters of the
    /// [`withdrawETH()`](withdrawETHCall) function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct withdrawETHReturn {}
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<withdrawETHCall> for UnderlyingRustTuple<'_> {
                fn from(value: withdrawETHCall) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for withdrawETHCall {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<withdrawETHReturn> for UnderlyingRustTuple<'_> {
                fn from(value: withdrawETHReturn) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for withdrawETHReturn {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        impl withdrawETHReturn {
            fn _tokenize(
                &self,
            ) -> <withdrawETHCall as alloy_sol_types::SolCall>::ReturnToken<'_>
            {
                ()
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for withdrawETHCall {
            type Parameters<'a> = ();
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = withdrawETHReturn;
            type ReturnTuple<'a> = ();
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str = "withdrawETH()";
            const SELECTOR: [u8; 4] = [224u8, 134u8, 229u8, 236u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                ()
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                withdrawETHReturn::_tokenize(ret)
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(Into::into)
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(Into::into)
            }
        }
    };
    #[derive(
        serde::Serialize,
        serde::Deserialize,
        Default,
        Debug,
        PartialEq,
        Eq,
        Hash,
    )]
    /**Function with signature `withdrawWETH()` and selector `0x4c02f62e`.
    ```solidity
    function withdrawWETH() external;
    ```*/
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct withdrawWETHCall;
    ///Container type for the return parameters of the
    /// [`withdrawWETH()`](withdrawWETHCall) function.
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields
    )]
    #[derive(Clone)]
    pub struct withdrawWETHReturn {}
    #[allow(
        non_camel_case_types,
        non_snake_case,
        clippy::pub_underscore_fields,
        clippy::style
    )]
    const _: () = {
        use alloy::sol_types as alloy_sol_types;
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<withdrawWETHCall> for UnderlyingRustTuple<'_> {
                fn from(value: withdrawWETHCall) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for withdrawWETHCall {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self
                }
            }
        }
        {
            #[doc(hidden)]
            type UnderlyingSolTuple<'a> = ();
            #[doc(hidden)]
            type UnderlyingRustTuple<'a> = ();
            #[cfg(test)]
            #[allow(dead_code, unreachable_patterns)]
            fn _type_assertion(
                _t: alloy_sol_types::private::AssertTypeEq<UnderlyingRustTuple>,
            ) {
                match _t {
                    alloy_sol_types::private::AssertTypeEq::<
                        <UnderlyingSolTuple as alloy_sol_types::SolType>::RustType,
                    >(_) => {}
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<withdrawWETHReturn> for UnderlyingRustTuple<'_> {
                fn from(value: withdrawWETHReturn) -> Self {
                    ()
                }
            }
            #[automatically_derived]
            #[doc(hidden)]
            impl ::core::convert::From<UnderlyingRustTuple<'_>> for withdrawWETHReturn {
                fn from(tuple: UnderlyingRustTuple<'_>) -> Self {
                    Self {}
                }
            }
        }
        impl withdrawWETHReturn {
            fn _tokenize(
                &self,
            ) -> <withdrawWETHCall as alloy_sol_types::SolCall>::ReturnToken<'_>
            {
                ()
            }
        }
        #[automatically_derived]
        impl alloy_sol_types::SolCall for withdrawWETHCall {
            type Parameters<'a> = ();
            type Token<'a> =
                <Self::Parameters<'a> as alloy_sol_types::SolType>::Token<'a>;
            type Return = withdrawWETHReturn;
            type ReturnTuple<'a> = ();
            type ReturnToken<'a> =
                <Self::ReturnTuple<'a> as alloy_sol_types::SolType>::Token<'a>;
            const SIGNATURE: &'static str = "withdrawWETH()";
            const SELECTOR: [u8; 4] = [76u8, 2u8, 246u8, 46u8];
            #[inline]
            fn new<'a>(
                tuple: <Self::Parameters<'a> as alloy_sol_types::SolType>::RustType,
            ) -> Self {
                tuple.into()
            }
            #[inline]
            fn tokenize(&self) -> Self::Token<'_> {
                ()
            }
            #[inline]
            fn tokenize_returns(ret: &Self::Return) -> Self::ReturnToken<'_> {
                withdrawWETHReturn::_tokenize(ret)
            }
            #[inline]
            fn abi_decode_returns(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence(data)
                    .map(Into::into)
            }
            #[inline]
            fn abi_decode_returns_validate(
                data: &[u8],
            ) -> alloy_sol_types::Result<Self::Return> {
                <Self::ReturnTuple<
                    '_,
                > as alloy_sol_types::SolType>::abi_decode_sequence_validate(data)
                    .map(Into::into)
            }
        }
    };
    ///Container for all the [`BlindArb`](self) function calls.
    #[derive(serde::Serialize, serde::Deserialize)]
    pub enum BlindArbCalls {
        #[allow(missing_docs)]
        execute_weth_token0(execute_weth_token0Call),
        #[allow(missing_docs)]
        execute_weth_token1(execute_weth_token1Call),
        #[allow(missing_docs)]
        owner(ownerCall),
        #[allow(missing_docs)]
        transferOwnership(transferOwnershipCall),
        #[allow(missing_docs)]
        uniswapV3SwapCallback(uniswapV3SwapCallbackCall),
        #[allow(missing_docs)]
        withdrawETH(withdrawETHCall),
        #[allow(missing_docs)]
        withdrawWETH(withdrawWETHCall),
    }
    #[automatically_derived]
    impl BlindArbCalls {
        /// All the selectors of this enum.
        ///
        /// Note that the selectors might not be in the same order as the
        /// variants. No guarantees are made about the order of the
        /// selectors.
        ///
        /// Prefer using `SolInterface` methods instead.
        pub const SELECTORS: &'static [[u8; 4usize]] = &[
            [37u8, 59u8, 33u8, 205u8],
            [76u8, 2u8, 246u8, 46u8],
            [141u8, 165u8, 203u8, 91u8],
            [224u8, 134u8, 229u8, 236u8],
            [237u8, 52u8, 184u8, 57u8],
            [242u8, 253u8, 227u8, 139u8],
            [250u8, 70u8, 30u8, 51u8],
        ];
    }
    #[automatically_derived]
    impl alloy_sol_types::SolInterface for BlindArbCalls {
        const NAME: &'static str = "BlindArbCalls";
        const MIN_DATA_LENGTH: usize = 0usize;
        const COUNT: usize = 7usize;
        #[inline]
        fn selector(&self) -> [u8; 4] {
            match self {
                Self::execute_weth_token0(_) => {
                    <execute_weth_token0Call as alloy_sol_types::SolCall>::SELECTOR
                }
                Self::execute_weth_token1(_) => {
                    <execute_weth_token1Call as alloy_sol_types::SolCall>::SELECTOR
                }
                Self::owner(_) => <ownerCall as alloy_sol_types::SolCall>::SELECTOR,
                Self::transferOwnership(_) => {
                    <transferOwnershipCall as alloy_sol_types::SolCall>::SELECTOR
                }
                Self::uniswapV3SwapCallback(_) => {
                    <uniswapV3SwapCallbackCall as alloy_sol_types::SolCall>::SELECTOR
                }
                Self::withdrawETH(_) => {
                    <withdrawETHCall as alloy_sol_types::SolCall>::SELECTOR
                }
                Self::withdrawWETH(_) => {
                    <withdrawWETHCall as alloy_sol_types::SolCall>::SELECTOR
                }
            }
        }
        #[inline]
        fn selector_at(i: usize) -> ::core::option::Option<[u8; 4]> {
            Self::SELECTORS.get(i).copied()
        }
        #[inline]
        fn valid_selector(selector: [u8; 4]) -> bool {
            Self::SELECTORS.binary_search(&selector).is_ok()
        }
        #[inline]
        #[allow(non_snake_case)]
        fn abi_decode_raw(
            selector: [u8; 4],
            data: &[u8],
        ) -> alloy_sol_types::Result<Self> {
            static DECODE_SHIMS: &[fn(
                &[u8],
            )
                -> alloy_sol_types::Result<
                BlindArbCalls,
            >] = &[
                {
                    fn execute_weth_token1(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <execute_weth_token1Call as alloy_sol_types::SolCall>::abi_decode_raw(
                                data,
                            )
                            .map(BlindArbCalls::execute_weth_token1)
                    }
                    execute_weth_token1
                },
                {
                    fn withdrawWETH(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <withdrawWETHCall as alloy_sol_types::SolCall>::abi_decode_raw(
                                data,
                            )
                            .map(BlindArbCalls::withdrawWETH)
                    }
                    withdrawWETH
                },
                {
                    fn owner(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <ownerCall as alloy_sol_types::SolCall>::abi_decode_raw(
                            data,
                        )
                        .map(BlindArbCalls::owner)
                    }
                    owner
                },
                {
                    fn withdrawETH(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <withdrawETHCall as alloy_sol_types::SolCall>::abi_decode_raw(
                                data,
                            )
                            .map(BlindArbCalls::withdrawETH)
                    }
                    withdrawETH
                },
                {
                    fn execute_weth_token0(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <execute_weth_token0Call as alloy_sol_types::SolCall>::abi_decode_raw(
                                data,
                            )
                            .map(BlindArbCalls::execute_weth_token0)
                    }
                    execute_weth_token0
                },
                {
                    fn transferOwnership(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <transferOwnershipCall as alloy_sol_types::SolCall>::abi_decode_raw(
                                data,
                            )
                            .map(BlindArbCalls::transferOwnership)
                    }
                    transferOwnership
                },
                {
                    fn uniswapV3SwapCallback(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <uniswapV3SwapCallbackCall as alloy_sol_types::SolCall>::abi_decode_raw(
                                data,
                            )
                            .map(BlindArbCalls::uniswapV3SwapCallback)
                    }
                    uniswapV3SwapCallback
                },
            ];
            let Ok(idx) = Self::SELECTORS.binary_search(&selector) else {
                return Err(
                    alloy_sol_types::Error::unknown_selector(
                        <Self as alloy_sol_types::SolInterface>::NAME,
                        selector,
                    ),
                );
            };
            DECODE_SHIMS[idx](data)
        }
        #[inline]
        #[allow(non_snake_case)]
        fn abi_decode_raw_validate(
            selector: [u8; 4],
            data: &[u8],
        ) -> alloy_sol_types::Result<Self> {
            static DECODE_VALIDATE_SHIMS: &[fn(
                &[u8],
            )
                -> alloy_sol_types::Result<
                BlindArbCalls,
            >] = &[
                {
                    fn execute_weth_token1(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <execute_weth_token1Call as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::execute_weth_token1)
                    }
                    execute_weth_token1
                },
                {
                    fn withdrawWETH(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <withdrawWETHCall as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::withdrawWETH)
                    }
                    withdrawWETH
                },
                {
                    fn owner(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <ownerCall as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::owner)
                    }
                    owner
                },
                {
                    fn withdrawETH(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <withdrawETHCall as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::withdrawETH)
                    }
                    withdrawETH
                },
                {
                    fn execute_weth_token0(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <execute_weth_token0Call as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::execute_weth_token0)
                    }
                    execute_weth_token0
                },
                {
                    fn transferOwnership(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <transferOwnershipCall as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::transferOwnership)
                    }
                    transferOwnership
                },
                {
                    fn uniswapV3SwapCallback(
                        data: &[u8],
                    ) -> alloy_sol_types::Result<BlindArbCalls>
                    {
                        <uniswapV3SwapCallbackCall as alloy_sol_types::SolCall>::abi_decode_raw_validate(
                                data,
                            )
                            .map(BlindArbCalls::uniswapV3SwapCallback)
                    }
                    uniswapV3SwapCallback
                },
            ];
            let Ok(idx) = Self::SELECTORS.binary_search(&selector) else {
                return Err(
                    alloy_sol_types::Error::unknown_selector(
                        <Self as alloy_sol_types::SolInterface>::NAME,
                        selector,
                    ),
                );
            };
            DECODE_VALIDATE_SHIMS[idx](data)
        }
        #[inline]
        fn abi_encoded_size(&self) -> usize {
            match self {
                Self::execute_weth_token0(inner) => {
                    <execute_weth_token0Call as alloy_sol_types::SolCall>::abi_encoded_size(
                        inner,
                    )
                }
                Self::execute_weth_token1(inner) => {
                    <execute_weth_token1Call as alloy_sol_types::SolCall>::abi_encoded_size(
                        inner,
                    )
                }
                Self::owner(inner) => {
                    <ownerCall as alloy_sol_types::SolCall>::abi_encoded_size(inner)
                }
                Self::transferOwnership(inner) => {
                    <transferOwnershipCall as alloy_sol_types::SolCall>::abi_encoded_size(
                        inner,
                    )
                }
                Self::uniswapV3SwapCallback(inner) => {
                    <uniswapV3SwapCallbackCall as alloy_sol_types::SolCall>::abi_encoded_size(
                        inner,
                    )
                }
                Self::withdrawETH(inner) => {
                    <withdrawETHCall as alloy_sol_types::SolCall>::abi_encoded_size(
                        inner,
                    )
                }
                Self::withdrawWETH(inner) => {
                    <withdrawWETHCall as alloy_sol_types::SolCall>::abi_encoded_size(
                        inner,
                    )
                }
            }
        }
        #[inline]
        fn abi_encode_raw(&self, out: &mut alloy_sol_types::private::Vec<u8>) {
            match self {
                Self::execute_weth_token0(inner) => {
                    <execute_weth_token0Call as alloy_sol_types::SolCall>::abi_encode_raw(
                        inner,
                        out,
                    )
                }
                Self::execute_weth_token1(inner) => {
                    <execute_weth_token1Call as alloy_sol_types::SolCall>::abi_encode_raw(
                        inner,
                        out,
                    )
                }
                Self::owner(inner) => {
                    <ownerCall as alloy_sol_types::SolCall>::abi_encode_raw(inner, out)
                }
                Self::transferOwnership(inner) => {
                    <transferOwnershipCall as alloy_sol_types::SolCall>::abi_encode_raw(
                        inner,
                        out,
                    )
                }
                Self::uniswapV3SwapCallback(inner) => {
                    <uniswapV3SwapCallbackCall as alloy_sol_types::SolCall>::abi_encode_raw(
                        inner,
                        out,
                    )
                }
                Self::withdrawETH(inner) => {
                    <withdrawETHCall as alloy_sol_types::SolCall>::abi_encode_raw(
                        inner,
                        out,
                    )
                }
                Self::withdrawWETH(inner) => {
                    <withdrawWETHCall as alloy_sol_types::SolCall>::abi_encode_raw(
                        inner,
                        out,
                    )
                }
            }
        }
    }
    ///Container for all the [`BlindArb`](self) events.
    #[derive(
        serde::Serialize, serde::Deserialize, Debug, PartialEq, Eq, Hash,
    )]
    pub enum BlindArbEvents {
        #[allow(missing_docs)]
        OwnershipTransferred(OwnershipTransferred),
    }
    #[automatically_derived]
    impl BlindArbEvents {
        /// All the selectors of this enum.
        ///
        /// Note that the selectors might not be in the same order as the
        /// variants. No guarantees are made about the order of the
        /// selectors.
        ///
        /// Prefer using `SolInterface` methods instead.
        pub const SELECTORS: &'static [[u8; 32usize]] = &[[
            139u8, 224u8, 7u8, 156u8, 83u8, 22u8, 89u8, 20u8, 19u8, 68u8,
            205u8, 31u8, 208u8, 164u8, 242u8, 132u8, 25u8, 73u8, 127u8, 151u8,
            34u8, 163u8, 218u8, 175u8, 227u8, 180u8, 24u8, 111u8, 107u8, 100u8,
            87u8, 224u8,
        ]];
    }
    #[automatically_derived]
    impl alloy_sol_types::SolEventInterface for BlindArbEvents {
        const NAME: &'static str = "BlindArbEvents";
        const COUNT: usize = 1usize;
        fn decode_raw_log(
            topics: &[alloy_sol_types::Word],
            data: &[u8],
        ) -> alloy_sol_types::Result<Self> {
            match topics.first().copied() {
                Some(
                    <OwnershipTransferred as alloy_sol_types::SolEvent>::SIGNATURE_HASH,
                ) => {
                    <OwnershipTransferred as alloy_sol_types::SolEvent>::decode_raw_log(
                            topics,
                            data,
                        )
                        .map(Self::OwnershipTransferred)
                }
                _ => {
                    alloy_sol_types::private::Err(alloy_sol_types::Error::InvalidLog {
                        name: <Self as alloy_sol_types::SolEventInterface>::NAME,
                        log: alloy_sol_types::private::Box::new(
                            alloy_sol_types::private::LogData::new_unchecked(
                                topics.to_vec(),
                                data.to_vec().into(),
                            ),
                        ),
                    })
                }
            }
        }
    }
    #[automatically_derived]
    impl alloy_sol_types::private::IntoLogData for BlindArbEvents {
        fn to_log_data(&self) -> alloy_sol_types::private::LogData {
            match self {
                Self::OwnershipTransferred(inner) => {
                    alloy_sol_types::private::IntoLogData::to_log_data(inner)
                }
            }
        }
        fn into_log_data(self) -> alloy_sol_types::private::LogData {
            match self {
                Self::OwnershipTransferred(inner) => {
                    alloy_sol_types::private::IntoLogData::into_log_data(inner)
                }
            }
        }
    }
    use alloy::contract as alloy_contract;
    /**Creates a new wrapper around an on-chain [`BlindArb`](self) contract instance.

    See the [wrapper's documentation](`BlindArbInstance`) for more details.*/
    #[inline]
    pub const fn new<
        P: alloy_contract::private::Provider<N>,
        N: alloy_contract::private::Network,
    >(
        address: alloy_sol_types::private::Address,
        provider: P,
    ) -> BlindArbInstance<P, N> {
        BlindArbInstance::<P, N>::new(address, provider)
    }
    /**Deploys this contract using the given `provider` and constructor arguments, if any.

    Returns a new instance of the contract, if the deployment was successful.

    For more fine-grained control over the deployment process, use [`deploy_builder`] instead.*/
    #[inline]
    pub fn deploy<
        P: alloy_contract::private::Provider<N>,
        N: alloy_contract::private::Network,
    >(
        provider: P,
    ) -> impl ::core::future::Future<
        Output = alloy_contract::Result<BlindArbInstance<P, N>>,
    > {
        BlindArbInstance::<P, N>::deploy(provider)
    }
    /**Creates a `RawCallBuilder` for deploying this contract using the given `provider`
    and constructor arguments, if any.

    This is a simple wrapper around creating a `RawCallBuilder` with the data set to
    the bytecode concatenated with the constructor's ABI-encoded arguments.*/
    #[inline]
    pub fn deploy_builder<
        P: alloy_contract::private::Provider<N>,
        N: alloy_contract::private::Network,
    >(
        provider: P,
    ) -> alloy_contract::RawCallBuilder<P, N> {
        BlindArbInstance::<P, N>::deploy_builder(provider)
    }
    /**A [`BlindArb`](self) instance.

    Contains type-safe methods for interacting with an on-chain instance of the
    [`BlindArb`](self) contract located at a given `address`, using a given
    provider `P`.

    If the contract bytecode is available (see the [`sol!`](alloy_sol_types::sol!)
    documentation on how to provide it), the `deploy` and `deploy_builder` methods can
    be used to deploy a new instance of the contract.

    See the [module-level documentation](self) for all the available methods.*/
    #[derive(Clone)]
    pub struct BlindArbInstance<P, N = alloy_contract::private::Ethereum> {
        address: alloy_sol_types::private::Address,
        provider: P,
        _network: ::core::marker::PhantomData<N>,
    }
    #[automatically_derived]
    impl<P, N> ::core::fmt::Debug for BlindArbInstance<P, N> {
        #[inline]
        fn fmt(
            &self,
            f: &mut ::core::fmt::Formatter<'_>,
        ) -> ::core::fmt::Result {
            f.debug_tuple("BlindArbInstance")
                .field(&self.address)
                .finish()
        }
    }
    /// Instantiation and getters/setters.
    #[automatically_derived]
    impl<
            P: alloy_contract::private::Provider<N>,
            N: alloy_contract::private::Network,
        > BlindArbInstance<P, N>
    {
        /**Creates a new wrapper around an on-chain [`BlindArb`](self) contract instance.

        See the [wrapper's documentation](`BlindArbInstance`) for more details.*/
        #[inline]
        pub const fn new(
            address: alloy_sol_types::private::Address,
            provider: P,
        ) -> Self {
            Self {
                address,
                provider,
                _network: ::core::marker::PhantomData,
            }
        }
        /**Deploys this contract using the given `provider` and constructor arguments, if any.

        Returns a new instance of the contract, if the deployment was successful.

        For more fine-grained control over the deployment process, use [`deploy_builder`] instead.*/
        #[inline]
        pub async fn deploy(
            provider: P,
        ) -> alloy_contract::Result<BlindArbInstance<P, N>> {
            let call_builder = Self::deploy_builder(provider);
            let contract_address = call_builder.deploy().await?;
            Ok(Self::new(
                contract_address,
                call_builder.provider,
            ))
        }
        /**Creates a `RawCallBuilder` for deploying this contract using the given `provider`
        and constructor arguments, if any.

        This is a simple wrapper around creating a `RawCallBuilder` with the data set to
        the bytecode concatenated with the constructor's ABI-encoded arguments.*/
        #[inline]
        pub fn deploy_builder(
            provider: P,
        ) -> alloy_contract::RawCallBuilder<P, N> {
            alloy_contract::RawCallBuilder::new_raw_deploy(
                provider,
                ::core::clone::Clone::clone(&BYTECODE),
            )
        }
        /// Returns a reference to the address.
        #[inline]
        pub const fn address(&self) -> &alloy_sol_types::private::Address {
            &self.address
        }
        /// Sets the address.
        #[inline]
        pub fn set_address(
            &mut self,
            address: alloy_sol_types::private::Address,
        ) {
            self.address = address;
        }
        /// Sets the address and returns `self`.
        pub fn at(
            mut self,
            address: alloy_sol_types::private::Address,
        ) -> Self {
            self.set_address(address);
            self
        }
        /// Returns a reference to the provider.
        #[inline]
        pub const fn provider(&self) -> &P {
            &self.provider
        }
    }
    impl<P: ::core::clone::Clone, N> BlindArbInstance<&P, N> {
        /// Clones the provider and returns a new instance with the cloned
        /// provider.
        #[inline]
        pub fn with_cloned_provider(self) -> BlindArbInstance<P, N> {
            BlindArbInstance {
                address: self.address,
                provider: ::core::clone::Clone::clone(&self.provider),
                _network: ::core::marker::PhantomData,
            }
        }
    }
    /// Function calls.
    #[automatically_derived]
    impl<
            P: alloy_contract::private::Provider<N>,
            N: alloy_contract::private::Network,
        > BlindArbInstance<P, N>
    {
        /// Creates a new call builder using this contract instance's provider
        /// and address.
        ///
        /// Note that the call can be any function call, not just those defined
        /// in this contract. Prefer using the other methods for
        /// building type-safe contract calls.
        pub fn call_builder<C: alloy_sol_types::SolCall>(
            &self,
            call: &C,
        ) -> alloy_contract::SolCallBuilder<&P, C, N> {
            alloy_contract::SolCallBuilder::new_sol(
                &self.provider,
                &self.address,
                call,
            )
        }
        ///Creates a new call builder for the [`execute_weth_token0`] function.
        pub fn execute_weth_token0(
            &self,
            v2Pool: alloy::sol_types::private::Address,
            v3Pool: alloy::sol_types::private::Address,
            amountIn: alloy::sol_types::private::primitives::aliases::U256,
            percentageToPayToCoinbase: alloy::sol_types::private::primitives::aliases::U256,
        ) -> alloy_contract::SolCallBuilder<&P, execute_weth_token0Call, N>
        {
            self.call_builder(&execute_weth_token0Call {
                v2Pool,
                v3Pool,
                amountIn,
                percentageToPayToCoinbase,
            })
        }
        ///Creates a new call builder for the [`execute_weth_token1`] function.
        pub fn execute_weth_token1(
            &self,
            v2Pool: alloy::sol_types::private::Address,
            v3Pool: alloy::sol_types::private::Address,
            amountIn: alloy::sol_types::private::primitives::aliases::U256,
            percentageToPayToCoinbase: alloy::sol_types::private::primitives::aliases::U256,
        ) -> alloy_contract::SolCallBuilder<&P, execute_weth_token1Call, N>
        {
            self.call_builder(&execute_weth_token1Call {
                v2Pool,
                v3Pool,
                amountIn,
                percentageToPayToCoinbase,
            })
        }
        ///Creates a new call builder for the [`owner`] function.
        pub fn owner(
            &self,
        ) -> alloy_contract::SolCallBuilder<&P, ownerCall, N> {
            self.call_builder(&ownerCall)
        }
        ///Creates a new call builder for the [`transferOwnership`] function.
        pub fn transferOwnership(
            &self,
            newOwner: alloy::sol_types::private::Address,
        ) -> alloy_contract::SolCallBuilder<&P, transferOwnershipCall, N>
        {
            self.call_builder(&transferOwnershipCall { newOwner })
        }
        ///Creates a new call builder for the [`uniswapV3SwapCallback`]
        /// function.
        pub fn uniswapV3SwapCallback(
            &self,
            amount0Delta: alloy::sol_types::private::primitives::aliases::I256,
            amount1Delta: alloy::sol_types::private::primitives::aliases::I256,
            data: alloy::sol_types::private::Bytes,
        ) -> alloy_contract::SolCallBuilder<&P, uniswapV3SwapCallbackCall, N>
        {
            self.call_builder(&uniswapV3SwapCallbackCall {
                amount0Delta,
                amount1Delta,
                data,
            })
        }
        ///Creates a new call builder for the [`withdrawETH`] function.
        pub fn withdrawETH(
            &self,
        ) -> alloy_contract::SolCallBuilder<&P, withdrawETHCall, N> {
            self.call_builder(&withdrawETHCall)
        }
        ///Creates a new call builder for the [`withdrawWETH`] function.
        pub fn withdrawWETH(
            &self,
        ) -> alloy_contract::SolCallBuilder<&P, withdrawWETHCall, N> {
            self.call_builder(&withdrawWETHCall)
        }
    }
    /// Event filters.
    #[automatically_derived]
    impl<
            P: alloy_contract::private::Provider<N>,
            N: alloy_contract::private::Network,
        > BlindArbInstance<P, N>
    {
        /// Creates a new event filter using this contract instance's provider
        /// and address.
        ///
        /// Note that the type can be any event, not just those defined in this
        /// contract. Prefer using the other methods for building
        /// type-safe event filters.
        pub fn event_filter<E: alloy_sol_types::SolEvent>(
            &self,
        ) -> alloy_contract::Event<&P, E, N> {
            alloy_contract::Event::new_sol(&self.provider, &self.address)
        }
        ///Creates a new event filter for the [`OwnershipTransferred`] event.
        pub fn OwnershipTransferred_filter(
            &self,
        ) -> alloy_contract::Event<&P, OwnershipTransferred, N> {
            self.event_filter::<OwnershipTransferred>()
        }
    }
}
